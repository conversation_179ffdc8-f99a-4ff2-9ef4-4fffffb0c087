import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { useAppStore } from '@/stores/appStore';
import { getFromStorage } from '@/utils';

export function useAuth() {
  const router = useRouter();
  const { user, token, login, logout, setLoading, setError } = useAuthStore();
  const { addNotification } = useAppStore();

  // Check voor bestaande sessie bij app start
  useEffect(() => {
    const checkExistingSession = async () => {
      const storedToken = getFromStorage('auth_token');
      
      if (storedToken && !user) {
        try {
          setLoading(true);
          
          // Verifieer token met backend
          const response = await fetch('/api/auth/verify', {
            headers: {
              'Authorization': `Bearer ${storedToken}`,
            },
          });

          if (response.ok) {
            const { user: userData } = await response.json();
            login(userData, storedToken);
          } else {
            // Token is ongeldig, verwijder uit storage
            logout();
          }
        } catch (error) {
          console.error('Session verification failed:', error);
          logout();
        } finally {
          setLoading(false);
        }
      }
    };

    checkExistingSession();
  }, [user, login, logout, setLoading]);

  const handleLogin = async (email: string, password: string, mfaCode?: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, mfaCode }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Inloggen mislukt');
      }

      login(data.user, data.token);
      
      // Set cookie voor middleware
      document.cookie = `auth_token=${data.token}; path=/; max-age=${8 * 60 * 60}`; // 8 uur

      addNotification({
        type: 'success',
        title: 'Welkom terug!',
        message: `Succesvol ingelogd als ${data.user.name}`,
      });

      return { success: true, requiresMFA: false };

    } catch (error: any) {
      const message = error.message || 'Er is een fout opgetreden';
      setError(message);
      
      addNotification({
        type: 'error',
        title: 'Inloggen mislukt',
        message,
      });

      // Check voor MFA requirement
      if (error.message?.includes('MFA') || message.includes('MFA')) {
        return { success: false, requiresMFA: true };
      }

      return { success: false, requiresMFA: false };
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    
    // Verwijder cookie
    document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
    
    addNotification({
      type: 'info',
      title: 'Uitgelogd',
      message: 'U bent succesvol uitgelogd',
    });

    router.push('/');
  };

  const isAdmin = user?.role === 'admin';
  const isAuthenticated = !!user && !!token;

  return {
    user,
    token,
    isAuthenticated,
    isAdmin,
    login: handleLogin,
    logout: handleLogout,
    loading: useAuthStore(state => state.isLoading),
    error: useAuthStore(state => state.error),
  };
}
