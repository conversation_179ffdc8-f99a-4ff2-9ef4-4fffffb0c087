"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/authStore";
import AppLayout from "@/components/layout/AppLayout";
import CalendarView from "@/components/schedule/CalendarView";
import AddAppointmentModal from "@/components/schedule/AddAppointmentModal";
import { ScheduleItem } from "@/types";

export default function AgendaPage() {
  const router = useRouter();
  const { user } = useAuthStore();
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    if (!user) {
      router.push("/");
    }
  }, [user, router]);

  const handleAddAppointment = (date?: Date) => {
    setSelectedDate(date);
    setShowAddModal(true);
  };

  const handleEditAppointment = (appointment: ScheduleItem) => {
    // TODO: Implement edit functionality
    console.log("Edit appointment:", appointment);
  };

  const handleAddSuccess = () => {
    setRefreshKey((prev) => prev + 1);
  };

  if (!user) {
    return null;
  }

  return (
    <AppLayout>
      <CalendarView
        key={refreshKey}
        onAddAppointment={handleAddAppointment}
        onEditAppointment={handleEditAppointment}
      />

      <AddAppointmentModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={handleAddSuccess}
        defaultDate={selectedDate}
      />
    </AppLayout>
  );
}
