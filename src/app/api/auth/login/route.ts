import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import { User } from "@/types";

// Mock gebruikers data - vervang dit met echte database calls
const mockUsers = [
  {
    id: "1",
    email: "<EMAIL>",
    password: "admin123!", // In productie: gehashed wachtwoord
    name: "Ad<PERSON> Gebruiker",
    role: "admin" as const,
    tenantId: "zorgorganisatie",
    isActive: true,
    mfaEnabled: false,
  },
  {
    id: "2",
    email: "<EMAIL>",
    password: "zorg123!", // In productie: gehashed wachtwoord
    name: "Zorgverlener Test",
    role: "user" as const,
    tenantId: "zorgorganisatie",
    isActive: true,
    mfaEnabled: false,
  },
];

const JWT_SECRET =
  process.env.JWT_SECRET || "your-secret-key-change-in-production";

export async function POST(request: NextRequest) {
  try {
    const { email, password, mfaCode } = await request.json();

    // Validatie
    if (!email || !password) {
      return NextResponse.json(
        { success: false, message: "E-mailadres en wachtwoord zijn verplicht" },
        { status: 400 }
      );
    }

    // Zoek gebruiker
    const user = mockUsers.find((u) => u.email === email);
    if (!user) {
      return NextResponse.json(
        { success: false, message: "Ongeldige inloggegevens" },
        { status: 401 }
      );
    }

    // Controleer wachtwoord (in productie: gebruik bcrypt)
    if (user.password !== password) {
      return NextResponse.json(
        { success: false, message: "Ongeldige inloggegevens" },
        { status: 401 }
      );
    }

    // Controleer of gebruiker actief is
    if (!user.isActive) {
      return NextResponse.json(
        { success: false, message: "Account is gedeactiveerd" },
        { status: 401 }
      );
    }

    // MFA controle
    if (user.mfaEnabled && !mfaCode) {
      return NextResponse.json(
        {
          success: false,
          message: "MFA verificatie vereist",
          requiresMFA: true,
        },
        { status: 401 }
      );
    }

    if (user.mfaEnabled && mfaCode) {
      // Mock MFA verificatie - in productie: gebruik echte MFA service
      if (mfaCode !== "123456") {
        return NextResponse.json(
          { success: false, message: "Ongeldige MFA code" },
          { status: 401 }
        );
      }
    }

    // Maak JWT token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId,
    };

    const token = jwt.sign(tokenPayload, JWT_SECRET, {
      expiresIn: "8h", // 8 uur sessie
    });

    // Gebruiker data zonder wachtwoord
    const userData: User = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      tenantId: user.tenantId,
      isActive: user.isActive,
      lastLogin: new Date(),
      mfaEnabled: user.mfaEnabled,
    };

    return NextResponse.json({
      success: true,
      user: userData,
      token,
      message: "Succesvol ingelogd",
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { success: false, message: "Er is een serverfout opgetreden" },
      { status: 500 }
    );
  }
}
