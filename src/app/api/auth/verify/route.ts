import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { User } from '@/types';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Mock gebruikers data - vervang dit met echte database calls
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '<PERSON><PERSON> Gebruiker',
    role: 'admin' as const,
    tenantId: 'zorgorganisatie',
    isActive: true,
    mfaEnabled: true,
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Zorgverlener Test',
    role: 'user' as const,
    tenantId: 'zorgorganisatie',
    isActive: true,
    mfaEnabled: false,
  },
];

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Geen geldig token gevonden' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verifieer JWT token
    const payload = jwt.verify(token, JWT_SECRET) as any;

    // Zoek gebruiker in database (mock)
    const user = mockUsers.find(u => u.id === payload.userId);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Gebruiker niet gevonden' },
        { status: 401 }
      );
    }

    if (!user.isActive) {
      return NextResponse.json(
        { success: false, message: 'Account is gedeactiveerd' },
        { status: 401 }
      );
    }

    // Gebruiker data zonder gevoelige informatie
    const userData: User = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      tenantId: user.tenantId,
      isActive: user.isActive,
      lastLogin: new Date(),
      mfaEnabled: user.mfaEnabled,
    };

    return NextResponse.json({
      success: true,
      user: userData,
      message: 'Token is geldig',
    });

  } catch (error) {
    console.error('Token verification error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { success: false, message: 'Ongeldig token' },
        { status: 401 }
      );
    }

    if (error instanceof jwt.TokenExpiredError) {
      return NextResponse.json(
        { success: false, message: 'Token is verlopen' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}
