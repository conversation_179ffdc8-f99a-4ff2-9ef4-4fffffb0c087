import { NextRequest, NextResponse } from 'next/server';
import { mockBillingMetrics, mockTenants, mockUsers } from '@/lib/mockAdminData';
import { mockClients, mockReports, mockScheduleItems } from '@/lib/mockData';
import { BillingMetrics } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // If specific tenant requested
    if (tenantId) {
      const metrics = calculateMetricsForTenant(tenantId, startDate, endDate);
      return NextResponse.json({
        success: true,
        data: metrics,
      });
    }

    // Return all tenant metrics
    const allMetrics = mockTenants
      .filter(tenant => tenant.isActive)
      .map(tenant => calculateMetricsForTenant(tenant.id, startDate, endDate));

    return NextResponse.json({
      success: true,
      data: allMetrics,
    });

  } catch (error) {
    console.error('Billing metrics API error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}

function calculateMetricsForTenant(
  tenantId: string, 
  startDateStr?: string | null, 
  endDateStr?: string | null
): BillingMetrics {
  const startDate = startDateStr ? new Date(startDateStr) : new Date(new Date().getFullYear(), new Date().getMonth(), 1);
  const endDate = endDateStr ? new Date(endDateStr) : new Date();

  // Count staff members for this tenant
  const staffCount = mockUsers.filter(user => 
    user.tenantId === tenantId && user.isActive
  ).length;

  // Count active clients for this tenant
  const clientCount = mockClients.filter(client => 
    client.tenantId === tenantId && client.isActive
  ).length;

  // Count reports in date range for this tenant
  const reportCount = mockReports.filter(report => {
    const reportDate = new Date(report.reportDate);
    return report.tenantId === tenantId && 
           reportDate >= startDate && 
           reportDate <= endDate;
  }).length;

  // Count schedule items in date range for this tenant
  const scheduleItemCount = mockScheduleItems.filter(item => {
    const itemDate = new Date(item.startTime);
    return item.tenantId === tenantId && 
           itemDate >= startDate && 
           itemDate <= endDate;
  }).length;

  // Estimate activity count (simplified calculation)
  const activityCount = reportCount + scheduleItemCount + (staffCount * 10);

  return {
    tenantId,
    period: {
      start: startDate,
      end: endDate,
    },
    staffCount,
    clientCount,
    reportCount,
    scheduleItemCount,
    activityCount,
  };
}
