import { NextRequest, NextResponse } from "next/server";
import {
  getClientById,
  getCareGoalsByClientId,
  getReportsByClientId,
  getScheduleItemsByClientId,
  mockClients,
} from "@/lib/mockData";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const client = getClientById(id);

    if (!client) {
      return NextResponse.json(
        { success: false, message: "Cliënt niet gevonden" },
        { status: 404 }
      );
    }

    const careGoals = getCareGoalsByClientId(id);
    const reports = getReportsByClientId(id);
    const scheduleItems = getScheduleItemsByClientId(id);

    return NextResponse.json({
      success: true,
      data: {
        client,
        careGoals,
        reports,
        scheduleItems,
      },
    });
  } catch (error) {
    console.error("Get client error:", error);
    return NextResponse.json(
      { success: false, message: "Er is een serverfout opgetreden" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { isActive } = await request.json();

    const clientIndex = mockClients.findIndex((c) => c.id === id);

    if (clientIndex === -1) {
      return NextResponse.json(
        { success: false, message: "Cliënt niet gevonden" },
        { status: 404 }
      );
    }

    // Update client
    mockClients[clientIndex] = {
      ...mockClients[clientIndex],
      isActive:
        isActive !== undefined ? isActive : mockClients[clientIndex].isActive,
      updatedAt: new Date(),
    };

    return NextResponse.json({
      success: true,
      data: mockClients[clientIndex],
      message: "Cliënt succesvol bijgewerkt",
    });
  } catch (error) {
    console.error("Update client error:", error);
    return NextResponse.json(
      { success: false, message: "Er is een serverfout opgetreden" },
      { status: 500 }
    );
  }
}
