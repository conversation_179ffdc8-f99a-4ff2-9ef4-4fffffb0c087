import { NextRequest, NextResponse } from 'next/server';
import { mockClients, getActiveClients } from '@/lib/mockData';
import { Client } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get('active') === 'true';
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    let clients = activeOnly ? getActiveClients() : mockClients;

    // Filter by search term
    if (search) {
      clients = clients.filter(client =>
        client.clientCode.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedClients = clients.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedClients,
      pagination: {
        page,
        limit,
        total: clients.length,
        totalPages: Math.ceil(clients.length / limit),
      },
    });

  } catch (error) {
    console.error('Clients API error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { clientCode } = await request.json();

    if (!clientCode) {
      return NextResponse.json(
        { success: false, message: 'Cliëntcode is verplicht' },
        { status: 400 }
      );
    }

    // Check if client code already exists
    const existingClient = mockClients.find(c => c.clientCode === clientCode);
    if (existingClient) {
      return NextResponse.json(
        { success: false, message: 'Cliëntcode bestaat al' },
        { status: 409 }
      );
    }

    const newClient: Client = {
      id: `C${String(mockClients.length + 1).padStart(3, '0')}`,
      tenantId: 'zorgorganisatie', // Should come from JWT token
      clientCode,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // In real app, save to database
    mockClients.push(newClient);

    return NextResponse.json({
      success: true,
      data: newClient,
      message: 'Cliënt succesvol toegevoegd',
    });

  } catch (error) {
    console.error('Create client error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}
