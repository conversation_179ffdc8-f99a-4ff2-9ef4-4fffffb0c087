import { NextRequest, NextResponse } from 'next/server';
import { mockScheduleItems, mockClients } from '@/lib/mockData';
import { ScheduleItem } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const clientId = searchParams.get('clientId');
    const status = searchParams.get('status');

    let scheduleItems = [...mockScheduleItems];

    // Filter by date range
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      scheduleItems = scheduleItems.filter(item => 
        item.startTime >= start && item.startTime <= end
      );
    }

    // Filter by client
    if (clientId) {
      scheduleItems = scheduleItems.filter(item => item.clientId === clientId);
    }

    // Filter by status
    if (status) {
      scheduleItems = scheduleItems.filter(item => item.status === status);
    }

    // Add client information
    const enrichedItems = scheduleItems.map(item => {
      const client = mockClients.find(c => c.id === item.clientId);
      return {
        ...item,
        clientCode: client?.clientCode || 'Onbekend',
      };
    });

    return NextResponse.json({
      success: true,
      data: enrichedItems,
    });

  } catch (error) {
    console.error('Schedule API error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { clientId, title, description, startTime, endTime, careGoalIds } = await request.json();

    if (!clientId || !title || !startTime || !endTime) {
      return NextResponse.json(
        { success: false, message: 'Verplichte velden ontbreken' },
        { status: 400 }
      );
    }

    const start = new Date(startTime);
    const end = new Date(endTime);

    if (start >= end) {
      return NextResponse.json(
        { success: false, message: 'Eindtijd moet na starttijd zijn' },
        { status: 400 }
      );
    }

    // Check for conflicts
    const conflicts = mockScheduleItems.filter(item => {
      const itemStart = new Date(item.startTime);
      const itemEnd = new Date(item.endTime);
      
      return item.clientId === clientId && 
             item.status !== 'cancelled' &&
             ((start >= itemStart && start < itemEnd) ||
              (end > itemStart && end <= itemEnd) ||
              (start <= itemStart && end >= itemEnd));
    });

    if (conflicts.length > 0) {
      return NextResponse.json(
        { success: false, message: 'Er is een tijdconflict met een bestaande afspraak' },
        { status: 409 }
      );
    }

    const newScheduleItem: ScheduleItem = {
      id: `S${String(mockScheduleItems.length + 1).padStart(3, '0')}`,
      tenantId: 'zorgorganisatie', // Should come from JWT token
      clientId,
      staffId: '2', // Should come from JWT token
      title,
      description: description || '',
      startTime: start,
      endTime: end,
      status: 'scheduled',
      careGoalIds: careGoalIds || [],
      createdBy: '2', // Should come from JWT token
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // In real app, save to database
    mockScheduleItems.push(newScheduleItem);

    return NextResponse.json({
      success: true,
      data: newScheduleItem,
      message: 'Afspraak succesvol gepland',
    });

  } catch (error) {
    console.error('Create schedule error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}
