import { NextRequest, NextResponse } from 'next/server';
import { 
  mockExtendedActivityLogs, 
  getActivityLogsByTenant, 
  getActivityLogsByUser,
  getUserById,
  getActionDisplayName
} from '@/lib/mockAdminData';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const userId = searchParams.get('userId');
    const action = searchParams.get('action');
    const resource = searchParams.get('resource');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    let logs = [...mockExtendedActivityLogs];

    // Filter by tenant
    if (tenantId) {
      logs = getActivityLogsByTenant(tenantId);
    }

    // Filter by user
    if (userId) {
      logs = logs.filter(log => log.userId === userId);
    }

    // Filter by action
    if (action) {
      logs = logs.filter(log => log.action === action);
    }

    // Filter by resource
    if (resource) {
      logs = logs.filter(log => log.resource === resource);
    }

    // Filter by date range
    if (dateFrom) {
      const fromDate = new Date(dateFrom);
      logs = logs.filter(log => new Date(log.timestamp) >= fromDate);
    }

    if (dateTo) {
      const toDate = new Date(dateTo);
      toDate.setHours(23, 59, 59, 999);
      logs = logs.filter(log => new Date(log.timestamp) <= toDate);
    }

    // Sort by timestamp (newest first)
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Add user information and action display names
    const enrichedLogs = logs.map(log => {
      const user = getUserById(log.userId);
      return {
        ...log,
        userName: user?.name || 'Onbekende gebruiker',
        userEmail: user?.email || 'Onbekend',
        actionDisplayName: getActionDisplayName(log.action),
      };
    });

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLogs = enrichedLogs.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedLogs,
      pagination: {
        page,
        limit,
        total: enrichedLogs.length,
        totalPages: Math.ceil(enrichedLogs.length / limit),
      },
    });

  } catch (error) {
    console.error('Activity logs API error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}
