import { NextRequest, NextResponse } from 'next/server';
import { mockTenants, getActiveTenants } from '@/lib/mockAdminData';
import { Tenant } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get('active') === 'true';
    const search = searchParams.get('search');

    let tenants = activeOnly ? getActiveTenants() : mockTenants;

    // Filter by search term
    if (search) {
      const searchLower = search.toLowerCase();
      tenants = tenants.filter(tenant =>
        tenant.name.toLowerCase().includes(searchLower) ||
        tenant.domain.toLowerCase().includes(searchLower)
      );
    }

    // Sort by name
    tenants.sort((a, b) => a.name.localeCompare(b.name));

    return NextResponse.json({
      success: true,
      data: tenants,
    });

  } catch (error) {
    console.error('Tenants API error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, domain, language, timezone, features } = await request.json();

    if (!name || !domain) {
      return NextResponse.json(
        { success: false, message: 'Naam en domein zijn verplicht' },
        { status: 400 }
      );
    }

    // Validate domain format
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$/;
    if (!domainRegex.test(domain)) {
      return NextResponse.json(
        { success: false, message: 'Ongeldig domein format' },
        { status: 400 }
      );
    }

    // Check if domain already exists
    const existingTenant = mockTenants.find(t => t.domain === domain);
    if (existingTenant) {
      return NextResponse.json(
        { success: false, message: 'Domein is al in gebruik' },
        { status: 409 }
      );
    }

    // Generate tenant ID from domain
    const tenantId = domain.split('.')[0].toLowerCase();

    // Check if tenant ID already exists
    const existingTenantId = mockTenants.find(t => t.id === tenantId);
    if (existingTenantId) {
      return NextResponse.json(
        { success: false, message: 'Tenant ID is al in gebruik' },
        { status: 409 }
      );
    }

    const newTenant: Tenant = {
      id: tenantId,
      name,
      domain,
      isActive: true,
      createdAt: new Date(),
      settings: {
        language: language || 'nl',
        timezone: timezone || 'Europe/Amsterdam',
        features: {
          billing: features?.billing || false,
          reporting: features?.reporting || true,
          scheduling: features?.scheduling || true,
        },
      },
    };

    // In real app, save to database
    mockTenants.push(newTenant);

    return NextResponse.json({
      success: true,
      data: newTenant,
      message: 'Tenant succesvol toegevoegd',
    });

  } catch (error) {
    console.error('Create tenant error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}
