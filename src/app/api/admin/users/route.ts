import { NextRequest, NextResponse } from 'next/server';
import { mockUsers, getUsersByTenant } from '@/lib/mockAdminData';
import { User } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const activeOnly = searchParams.get('active') === 'true';
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    let users = tenantId ? getUsersByTenant(tenantId) : mockUsers;

    // Filter by active status
    if (activeOnly) {
      users = users.filter(user => user.isActive);
    }

    // Filter by search term
    if (search) {
      const searchLower = search.toLowerCase();
      users = users.filter(user =>
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower)
      );
    }

    // Sort by name
    users.sort((a, b) => a.name.localeCompare(b.name));

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = users.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedUsers,
      pagination: {
        page,
        limit,
        total: users.length,
        totalPages: Math.ceil(users.length / limit),
      },
    });

  } catch (error) {
    console.error('Users API error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { email, name, role, tenantId, mfaEnabled } = await request.json();

    if (!email || !name || !role || !tenantId) {
      return NextResponse.json(
        { success: false, message: 'Verplichte velden ontbreken' },
        { status: 400 }
      );
    }

    // Validate email format (FQDN)
    const emailRegex = /^[^\s@]+@[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, message: 'Ongeldig e-mailadres format (gebruik <EMAIL>)' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUser = mockUsers.find(u => u.email === email);
    if (existingUser) {
      return NextResponse.json(
        { success: false, message: 'E-mailadres is al in gebruik' },
        { status: 409 }
      );
    }

    const newUser: User = {
      id: `U${String(mockUsers.length + 1).padStart(3, '0')}`,
      email,
      name,
      role: role as 'admin' | 'user',
      tenantId,
      isActive: true,
      mfaEnabled: mfaEnabled || false,
      lastLogin: undefined,
    };

    // In real app, save to database
    mockUsers.push(newUser);

    return NextResponse.json({
      success: true,
      data: newUser,
      message: 'Gebruiker succesvol toegevoegd',
    });

  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}
