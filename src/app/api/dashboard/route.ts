import { NextRequest, NextResponse } from 'next/server';
import { mockClients } from '@/lib/mockData';
import { mockScheduleItems } from '@/lib/mockData';
import { mockReports } from '@/lib/mockData';

export async function GET(request: NextRequest) {
  try {
    // Calculate dashboard metrics
    const totalClients = mockClients.length;
    const activeClients = mockClients.filter(client => client.isActive).length;
    
    // Today's schedule
    const today = new Date().toISOString().split('T')[0];
    const todaySchedule = mockScheduleItems.filter(item => 
      item.date === today
    );
    
    // Pending goals (simplified calculation)
    const pendingGoals = mockClients.reduce((total, client) => {
      return total + (client.careGoals?.filter(goal => goal.status === 'in_progress').length || 0);
    }, 0);
    
    // Overdue reports
    const overdueReports = mockReports.filter(report => {
      const reportDate = new Date(report.date);
      const today = new Date();
      return report.status !== 'complete' && reportDate < today;
    }).length;
    
    // Recent activities (mock data)
    const recentActivities = [
      {
        id: '1',
        type: 'client_added',
        description: 'Nieuwe cliënt toegevoegd',
        timestamp: new Date().toISOString(),
        user: 'Zorgverlener'
      },
      {
        id: '2',
        type: 'report_completed',
        description: 'Rapportage voltooid voor C001',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        user: 'Zorgverlener'
      },
      {
        id: '3',
        type: 'appointment_scheduled',
        description: 'Afspraak ingepland voor C003',
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        user: 'Zorgverlener'
      }
    ];
    
    const dashboardData = {
      metrics: {
        totalClients,
        activeClients,
        pendingGoals,
        overdueReports,
        todayAppointments: todaySchedule.length
      },
      todaySchedule: todaySchedule.slice(0, 5), // Limit to 5 items
      recentActivities: recentActivities.slice(0, 10), // Limit to 10 items
      quickStats: {
        completedReports: mockReports.filter(r => r.status === 'complete').length,
        partialReports: mockReports.filter(r => r.status === 'partial').length,
        missingReports: mockReports.filter(r => r.status === 'missing').length
      }
    };
    
    return NextResponse.json({
      success: true,
      data: dashboardData
    });
    
  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { success: false, message: 'Fout bij ophalen dashboard gegevens' },
      { status: 500 }
    );
  }
}
