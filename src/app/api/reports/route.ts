import { NextRequest, NextResponse } from 'next/server';
import { mockReports, mockClients, mockCareGoals } from '@/lib/mockData';
import { Report } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get('clientId');
    const careGoalId = searchParams.get('careGoalId');
    const status = searchParams.get('status');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    let reports = [...mockReports];

    // Filter by client
    if (clientId) {
      reports = reports.filter(report => report.clientId === clientId);
    }

    // Filter by care goal
    if (careGoalId) {
      reports = reports.filter(report => report.careGoalId === careGoalId);
    }

    // Filter by status
    if (status) {
      reports = reports.filter(report => report.status === status);
    }

    // Filter by date range
    if (dateFrom) {
      const fromDate = new Date(dateFrom);
      reports = reports.filter(report => new Date(report.reportDate) >= fromDate);
    }

    if (dateTo) {
      const toDate = new Date(dateTo);
      reports = reports.filter(report => new Date(report.reportDate) <= toDate);
    }

    // Sort by report date (newest first)
    reports.sort((a, b) => new Date(b.reportDate).getTime() - new Date(a.reportDate).getTime());

    // Add client and care goal information
    const enrichedReports = reports.map(report => {
      const client = mockClients.find(c => c.id === report.clientId);
      const careGoal = report.careGoalId ? mockCareGoals.find(g => g.id === report.careGoalId) : null;
      
      return {
        ...report,
        clientCode: client?.clientCode || 'Onbekend',
        careGoalTitle: careGoal?.title || null,
      };
    });

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedReports = enrichedReports.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedReports,
      pagination: {
        page,
        limit,
        total: enrichedReports.length,
        totalPages: Math.ceil(enrichedReports.length / limit),
      },
    });

  } catch (error) {
    console.error('Reports API error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { clientId, careGoalId, title, content, reportDate } = await request.json();

    if (!clientId || !title || !reportDate) {
      return NextResponse.json(
        { success: false, message: 'Verplichte velden ontbreken' },
        { status: 400 }
      );
    }

    // Validate client exists
    const client = mockClients.find(c => c.id === clientId);
    if (!client) {
      return NextResponse.json(
        { success: false, message: 'Cliënt niet gevonden' },
        { status: 404 }
      );
    }

    // Validate care goal if provided
    if (careGoalId) {
      const careGoal = mockCareGoals.find(g => g.id === careGoalId && g.clientId === clientId);
      if (!careGoal) {
        return NextResponse.json(
          { success: false, message: 'Zorgdoel niet gevonden voor deze cliënt' },
          { status: 404 }
        );
      }
    }

    const newReport: Report = {
      id: `R${String(mockReports.length + 1).padStart(3, '0')}`,
      clientId,
      careGoalId: careGoalId || undefined,
      tenantId: 'zorgorganisatie', // Should come from JWT token
      title,
      content: content || '',
      status: content && content.trim() ? 'complete' : 'partial',
      reportDate: new Date(reportDate),
      createdBy: '2', // Should come from JWT token
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // In real app, save to database
    mockReports.push(newReport);

    return NextResponse.json({
      success: true,
      data: newReport,
      message: 'Rapportage succesvol toegevoegd',
    });

  } catch (error) {
    console.error('Create report error:', error);
    return NextResponse.json(
      { success: false, message: 'Er is een serverfout opgetreden' },
      { status: 500 }
    );
  }
}
