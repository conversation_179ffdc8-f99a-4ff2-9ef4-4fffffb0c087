"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/authStore";
import AppLayout from "@/components/layout/AppLayout";
import BillingOverview from "@/components/billing/BillingOverview";
import { Badge } from "@/components/ui";

export default function FacturatiePage() {
  const router = useRouter();
  const { user } = useAuthStore();

  useEffect(() => {
    if (!user) {
      router.push("/");
    } else if (user.role !== "admin") {
      router.push("/dashboard");
    }
  }, [user, router]);

  if (!user || user.role !== "admin") {
    return null;
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Facturatie</h1>
            <p className="text-gray-600 mt-1">
              Beheer facturatie en billing metrics
            </p>
          </div>
          <Badge variant="info">Admin Only</Badge>
        </div>

        <BillingOverview />
      </div>
    </AppLayout>
  );
}
