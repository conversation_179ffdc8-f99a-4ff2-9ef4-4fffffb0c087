"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/authStore";
import AppLayout from "@/components/layout/AppLayout";
import ClientDetail from "@/components/clients/ClientDetail";

interface ClientDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ClientDetailPage({ params }: ClientDetailPageProps) {
  const router = useRouter();
  const { user } = useAuthStore();
  const [clientId, setClientId] = useState<string>("");

  useEffect(() => {
    const getParams = async () => {
      const { id } = await params;
      setClientId(id);
    };
    getParams();
  }, [params]);

  useEffect(() => {
    if (!user) {
      router.push("/");
    }
  }, [user, router]);

  const handleBack = () => {
    router.push("/clienten");
  };

  if (!user || !clientId) {
    return null;
  }

  return (
    <AppLayout>
      <ClientDetail clientId={clientId} onBack={handleBack} />
    </AppLayout>
  );
}
