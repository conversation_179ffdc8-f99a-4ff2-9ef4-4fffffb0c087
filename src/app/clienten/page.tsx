"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/authStore";
import AppLayout from "@/components/layout/AppLayout";
import ClientList from "@/components/clients/ClientList";
import AddClientModal from "@/components/clients/AddClientModal";

export default function ClientenPage() {
  const router = useRouter();
  const { user } = useAuthStore();
  const [showAddModal, setShowAddModal] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    if (!user) {
      router.push("/");
    }
  }, [user, router]);

  const handleAddClient = () => {
    setShowAddModal(true);
  };

  const handleAddSuccess = () => {
    setRefreshKey((prev) => prev + 1);
  };

  if (!user) {
    return null;
  }

  return (
    <AppLayout>
      <ClientList key={refreshKey} onAddClient={handleAddClient} />

      <AddClientModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={handleAddSuccess}
      />
    </AppLayout>
  );
}
