"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/authStore";
import AppLayout from "@/components/layout/AppLayout";
import ReportsList from "@/components/reports/ReportsList";
import AddReportModal from "@/components/reports/AddReportModal";

export default function RapportagesPage() {
  const router = useRouter();
  const { user } = useAuthStore();
  const [showAddModal, setShowAddModal] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    if (!user) {
      router.push("/");
    }
  }, [user, router]);

  const handleAddReport = () => {
    setShowAddModal(true);
  };

  const handleAddSuccess = () => {
    setRefreshKey((prev) => prev + 1);
  };

  if (!user) {
    return null;
  }

  return (
    <AppLayout>
      <ReportsList key={refreshKey} onAddReport={handleAddReport} />

      <AddReportModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={handleAddSuccess}
      />
    </AppLayout>
  );
}
