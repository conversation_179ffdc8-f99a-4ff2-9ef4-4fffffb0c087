"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  UserGroupIcon,
  BuildingOfficeIcon,
  ClockIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";
import { useAuthStore } from "@/stores/authStore";
import AppLayout from "@/components/layout/AppLayout";
import UserManagement from "@/components/admin/UserManagement";
import ActivityLogs from "@/components/admin/ActivityLogs";
import { Badge } from "@/components/ui";

export default function BeheerPage() {
  const router = useRouter();
  const { user } = useAuthStore();
  const [activeTab, setActiveTab] = useState<
    "users" | "tenants" | "logs" | "monitoring"
  >("users");

  useEffect(() => {
    if (!user) {
      router.push("/");
    } else if (user.role !== "admin") {
      router.push("/dashboard");
    }
  }, [user, router]);

  if (!user || user.role !== "admin") {
    return null;
  }

  const tabs = [
    { id: "users", label: "Gebruikers", icon: UserGroupIcon },
    { id: "tenants", label: "Tenants", icon: BuildingOfficeIcon },
    { id: "logs", label: "Activiteit Logs", icon: ClockIcon },
    { id: "monitoring", label: "Monitoring", icon: ChartBarIcon },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Beheer</h1>
            <p className="text-gray-600 mt-1">
              Systeem beheer en gebruikers management
            </p>
          </div>
          <Badge variant="info">Admin Only</Badge>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === "users" && <UserManagement />}

        {activeTab === "tenants" && (
          <div className="text-center py-12">
            <BuildingOfficeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Tenant Beheer
            </h3>
            <p className="text-gray-500">
              Deze functionaliteit wordt binnenkort beschikbaar.
            </p>
          </div>
        )}

        {activeTab === "logs" && <ActivityLogs />}

        {activeTab === "monitoring" && (
          <div className="text-center py-12">
            <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Monitoring & Statistieken
            </h3>
            <p className="text-gray-500">
              Grafana dashboard integratie wordt binnenkort beschikbaar.
            </p>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
