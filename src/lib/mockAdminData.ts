import { User, Tenant, ActivityLog, BillingMetrics } from '@/types';

// Mock Users Data (extended)
export const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '<PERSON><PERSON>',
    role: 'admin',
    tenantId: 'zorgorganisatie',
    isActive: true,
    lastLogin: new Date('2024-08-07T08:30:00'),
    mfaEnabled: true,
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Zorgverlener Test',
    role: 'user',
    tenantId: 'zorgorganisatie',
    isActive: true,
    lastLogin: new Date('2024-08-07T09:15:00'),
    mfaEnabled: false,
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: 'Manager Zorg',
    role: 'user',
    tenantId: 'zorgorganisatie',
    isActive: true,
    lastLogin: new Date('2024-08-06T16:45:00'),
    mfaEnabled: true,
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: 'Medewerker Zorg',
    role: 'user',
    tenantId: 'zorgorganisatie',
    isActive: false,
    lastLogin: new Date('2024-07-15T14:20:00'),
    mfaEnabled: false,
  },
  {
    id: '5',
    email: '<EMAIL>',
    name: 'Admin Andere Zorg',
    role: 'admin',
    tenantId: 'anderezorg',
    isActive: true,
    lastLogin: new Date('2024-08-07T07:00:00'),
    mfaEnabled: true,
  },
];

// Mock Tenants Data
export const mockTenants: Tenant[] = [
  {
    id: 'zorgorganisatie',
    name: 'Zorgorganisatie Nederland',
    domain: 'zorgorganisatie.nl',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    settings: {
      language: 'nl',
      timezone: 'Europe/Amsterdam',
      features: {
        billing: true,
        reporting: true,
        scheduling: true,
      },
    },
  },
  {
    id: 'anderezorg',
    name: 'Andere Zorg BV',
    domain: 'anderezorg.nl',
    isActive: true,
    createdAt: new Date('2024-03-15'),
    settings: {
      language: 'nl',
      timezone: 'Europe/Amsterdam',
      features: {
        billing: false,
        reporting: true,
        scheduling: true,
      },
    },
  },
  {
    id: 'testzorg',
    name: 'Test Zorg Instelling',
    domain: 'testzorg.nl',
    isActive: false,
    createdAt: new Date('2024-02-10'),
    settings: {
      language: 'nl',
      timezone: 'Europe/Amsterdam',
      features: {
        billing: false,
        reporting: true,
        scheduling: false,
      },
    },
  },
];

// Extended Activity Logs
export const mockExtendedActivityLogs: ActivityLog[] = [
  {
    id: 'A001',
    tenantId: 'zorgorganisatie',
    userId: '2',
    action: 'CREATE_REPORT',
    resource: 'Report',
    resourceId: 'R001',
    timestamp: new Date('2024-08-07T14:30:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 'A002',
    tenantId: 'zorgorganisatie',
    userId: '1',
    action: 'UPDATE_USER',
    resource: 'User',
    resourceId: '2',
    timestamp: new Date('2024-08-07T09:15:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 'A003',
    tenantId: 'zorgorganisatie',
    userId: '2',
    action: 'CREATE_SCHEDULE',
    resource: 'ScheduleItem',
    resourceId: 'S002',
    timestamp: new Date('2024-08-07T11:20:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 'A004',
    tenantId: 'zorgorganisatie',
    userId: '1',
    action: 'LOGIN',
    resource: 'Auth',
    resourceId: '1',
    timestamp: new Date('2024-08-07T08:30:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 'A005',
    tenantId: 'zorgorganisatie',
    userId: '2',
    action: 'LOGIN',
    resource: 'Auth',
    resourceId: '2',
    timestamp: new Date('2024-08-07T09:15:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 'A006',
    tenantId: 'zorgorganisatie',
    userId: '3',
    action: 'CREATE_CLIENT',
    resource: 'Client',
    resourceId: 'C006',
    timestamp: new Date('2024-08-06T16:45:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
  },
  {
    id: 'A007',
    tenantId: 'zorgorganisatie',
    userId: '2',
    action: 'UPDATE_REPORT',
    resource: 'Report',
    resourceId: 'R003',
    timestamp: new Date('2024-08-06T14:20:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 'A008',
    tenantId: 'anderezorg',
    userId: '5',
    action: 'LOGIN',
    resource: 'Auth',
    resourceId: '5',
    timestamp: new Date('2024-08-07T07:00:00'),
    ipAddress: '*********',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
  },
];

// Mock Billing Metrics
export const mockBillingMetrics: BillingMetrics[] = [
  {
    tenantId: 'zorgorganisatie',
    period: {
      start: new Date('2024-08-01'),
      end: new Date('2024-08-31'),
    },
    staffCount: 4,
    clientCount: 5,
    reportCount: 15,
    scheduleItemCount: 8,
    activityCount: 45,
  },
  {
    tenantId: 'anderezorg',
    period: {
      start: new Date('2024-08-01'),
      end: new Date('2024-08-31'),
    },
    staffCount: 2,
    clientCount: 3,
    reportCount: 8,
    scheduleItemCount: 5,
    activityCount: 22,
  },
];

// Helper functions
export function getUsersByTenant(tenantId: string): User[] {
  return mockUsers.filter(user => user.tenantId === tenantId);
}

export function getActiveUsers(): User[] {
  return mockUsers.filter(user => user.isActive);
}

export function getActiveTenants(): Tenant[] {
  return mockTenants.filter(tenant => tenant.isActive);
}

export function getActivityLogsByTenant(tenantId: string): ActivityLog[] {
  return mockExtendedActivityLogs.filter(log => log.tenantId === tenantId);
}

export function getActivityLogsByUser(userId: string): ActivityLog[] {
  return mockExtendedActivityLogs.filter(log => log.userId === userId);
}

export function getBillingMetricsByTenant(tenantId: string): BillingMetrics | undefined {
  return mockBillingMetrics.find(metrics => metrics.tenantId === tenantId);
}

export function getUserById(id: string): User | undefined {
  return mockUsers.find(user => user.id === id);
}

export function getTenantById(id: string): Tenant | undefined {
  return mockTenants.find(tenant => tenant.id === id);
}

// Action type mappings for display
export const actionTypeMap: Record<string, string> = {
  'LOGIN': 'Inloggen',
  'LOGOUT': 'Uitloggen',
  'CREATE_CLIENT': 'Cliënt aangemaakt',
  'UPDATE_CLIENT': 'Cliënt bijgewerkt',
  'DELETE_CLIENT': 'Cliënt verwijderd',
  'CREATE_REPORT': 'Rapportage aangemaakt',
  'UPDATE_REPORT': 'Rapportage bijgewerkt',
  'DELETE_REPORT': 'Rapportage verwijderd',
  'CREATE_SCHEDULE': 'Afspraak gepland',
  'UPDATE_SCHEDULE': 'Afspraak bijgewerkt',
  'DELETE_SCHEDULE': 'Afspraak geannuleerd',
  'CREATE_USER': 'Gebruiker aangemaakt',
  'UPDATE_USER': 'Gebruiker bijgewerkt',
  'DELETE_USER': 'Gebruiker verwijderd',
  'CREATE_GOAL': 'Zorgdoel aangemaakt',
  'UPDATE_GOAL': 'Zorgdoel bijgewerkt',
  'DELETE_GOAL': 'Zorgdoel verwijderd',
};

export function getActionDisplayName(action: string): string {
  return actionTypeMap[action] || action;
}
