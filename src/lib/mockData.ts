import { Client, CareGoal, Report, ScheduleItem, User, ActivityLog } from '@/types';

// Mock Clients Data
export const mockClients: Client[] = [
  {
    id: 'C001',
    tenantId: 'zorgorganisatie',
    clientCode: 'C001',
    isActive: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-08-01'),
  },
  {
    id: 'C002',
    tenantId: 'zorgorganisatie',
    clientCode: 'C002',
    isActive: true,
    createdAt: new Date('2024-02-20'),
    updatedAt: new Date('2024-07-28'),
  },
  {
    id: 'C003',
    tenantId: 'zorgorganisatie',
    clientCode: 'C003',
    isActive: true,
    createdAt: new Date('2024-03-10'),
    updatedAt: new Date('2024-08-05'),
  },
  {
    id: 'C004',
    tenantId: 'zorgorganisatie',
    clientCode: 'C004',
    isActive: false,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-06-15'),
  },
  {
    id: 'C005',
    tenantId: 'zorgorganisatie',
    clientCode: 'C005',
    isActive: true,
    createdAt: new Date('2024-04-12'),
    updatedAt: new Date('2024-08-03'),
  },
];

// Mock Care Goals Data
export const mockCareGoals: CareGoal[] = [
  {
    id: 'G001',
    clientId: 'C001',
    tenantId: 'zorgorganisatie',
    title: 'Mobiliteit verbeteren',
    description: 'Dagelijkse oefeningen voor het verbeteren van de mobiliteit en balans.',
    status: 'active',
    priority: 'high',
    startDate: new Date('2024-07-01'),
    endDate: new Date('2024-09-01'),
    createdBy: '2',
    createdAt: new Date('2024-06-28'),
    updatedAt: new Date('2024-08-01'),
  },
  {
    id: 'G002',
    clientId: 'C001',
    tenantId: 'zorgorganisatie',
    title: 'Medicatie compliance',
    description: 'Zorgen voor correcte inname van voorgeschreven medicatie.',
    status: 'completed',
    priority: 'medium',
    startDate: new Date('2024-06-01'),
    endDate: new Date('2024-07-31'),
    createdBy: '2',
    createdAt: new Date('2024-05-28'),
    updatedAt: new Date('2024-07-31'),
  },
  {
    id: 'G003',
    clientId: 'C002',
    tenantId: 'zorgorganisatie',
    title: 'Sociale activiteiten',
    description: 'Deelname aan groepsactiviteiten ter bevordering van sociale interactie.',
    status: 'active',
    priority: 'medium',
    startDate: new Date('2024-07-15'),
    endDate: new Date('2024-10-15'),
    createdBy: '2',
    createdAt: new Date('2024-07-10'),
    updatedAt: new Date('2024-08-05'),
  },
  {
    id: 'G004',
    clientId: 'C003',
    tenantId: 'zorgorganisatie',
    title: 'Voeding optimaliseren',
    description: 'Verbetering van voedingspatroon en gewichtsbeheersing.',
    status: 'paused',
    priority: 'low',
    startDate: new Date('2024-06-01'),
    createdBy: '2',
    createdAt: new Date('2024-05-25'),
    updatedAt: new Date('2024-07-20'),
  },
  {
    id: 'G005',
    clientId: 'C005',
    tenantId: 'zorgorganisatie',
    title: 'Cognitieve training',
    description: 'Geheugen- en concentratieoefeningen ter ondersteuning van cognitieve functies.',
    status: 'active',
    priority: 'high',
    startDate: new Date('2024-07-20'),
    endDate: new Date('2024-11-20'),
    createdBy: '2',
    createdAt: new Date('2024-07-15'),
    updatedAt: new Date('2024-08-02'),
  },
];

// Mock Reports Data
export const mockReports: Report[] = [
  {
    id: 'R001',
    clientId: 'C001',
    careGoalId: 'G001',
    tenantId: 'zorgorganisatie',
    title: 'Mobiliteit voortgang week 1',
    content: 'Cliënt heeft deze week 5 van de 7 geplande oefeningen voltooid. Balans is merkbaar verbeterd.',
    status: 'complete',
    reportDate: new Date('2024-08-05'),
    createdBy: '2',
    createdAt: new Date('2024-08-05'),
    updatedAt: new Date('2024-08-05'),
  },
  {
    id: 'R002',
    clientId: 'C001',
    careGoalId: 'G002',
    tenantId: 'zorgorganisatie',
    title: 'Medicatie compliance evaluatie',
    content: 'Medicatie wordt correct ingenomen volgens schema. Geen bijwerkingen gemeld.',
    status: 'complete',
    reportDate: new Date('2024-07-31'),
    createdBy: '2',
    createdAt: new Date('2024-07-31'),
    updatedAt: new Date('2024-07-31'),
  },
  {
    id: 'R003',
    clientId: 'C002',
    careGoalId: 'G003',
    tenantId: 'zorgorganisatie',
    title: 'Sociale activiteiten deelname',
    content: 'Actieve deelname aan groepsactiviteiten. Positieve interactie met andere deelnemers.',
    status: 'partial',
    reportDate: new Date('2024-08-03'),
    createdBy: '2',
    createdAt: new Date('2024-08-04'),
    updatedAt: new Date('2024-08-04'),
  },
  {
    id: 'R004',
    clientId: 'C003',
    tenantId: 'zorgorganisatie',
    title: 'Algemene observatie',
    content: 'Algemene toestand stabiel. Geen bijzonderheden te melden.',
    status: 'complete',
    reportDate: new Date('2024-08-02'),
    createdBy: '2',
    createdAt: new Date('2024-08-02'),
    updatedAt: new Date('2024-08-02'),
  },
  {
    id: 'R005',
    clientId: 'C005',
    careGoalId: 'G005',
    tenantId: 'zorgorganisatie',
    title: 'Cognitieve training week 2',
    content: '',
    status: 'missing',
    reportDate: new Date('2024-08-01'),
    createdBy: '2',
    createdAt: new Date('2024-08-06'),
    updatedAt: new Date('2024-08-06'),
  },
];

// Mock Schedule Items
export const mockScheduleItems: ScheduleItem[] = [
  {
    id: 'S001',
    tenantId: 'zorgorganisatie',
    clientId: 'C001',
    staffId: '2',
    title: 'Mobiliteit training',
    description: 'Begeleide oefeningen voor mobiliteit',
    startTime: new Date('2024-08-08T09:00:00'),
    endTime: new Date('2024-08-08T10:00:00'),
    status: 'scheduled',
    careGoalIds: ['G001'],
    createdBy: '2',
    createdAt: new Date('2024-08-05'),
    updatedAt: new Date('2024-08-05'),
  },
  {
    id: 'S002',
    tenantId: 'zorgorganisatie',
    clientId: 'C002',
    staffId: '2',
    title: 'Groepsactiviteit',
    description: 'Sociale activiteit in groepsverband',
    startTime: new Date('2024-08-08T14:00:00'),
    endTime: new Date('2024-08-08T15:30:00'),
    status: 'scheduled',
    careGoalIds: ['G003'],
    createdBy: '2',
    createdAt: new Date('2024-08-06'),
    updatedAt: new Date('2024-08-06'),
  },
  {
    id: 'S003',
    tenantId: 'zorgorganisatie',
    clientId: 'C005',
    staffId: '2',
    title: 'Cognitieve training',
    description: 'Individuele cognitieve oefeningen',
    startTime: new Date('2024-08-07T10:30:00'),
    endTime: new Date('2024-08-07T11:30:00'),
    status: 'completed',
    careGoalIds: ['G005'],
    createdBy: '2',
    createdAt: new Date('2024-08-05'),
    updatedAt: new Date('2024-08-07'),
  },
];

// Mock Activity Logs
export const mockActivityLogs: ActivityLog[] = [
  {
    id: 'A001',
    tenantId: 'zorgorganisatie',
    userId: '2',
    action: 'CREATE_REPORT',
    resource: 'Report',
    resourceId: 'R001',
    timestamp: new Date('2024-08-05T14:30:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 'A002',
    tenantId: 'zorgorganisatie',
    userId: '1',
    action: 'UPDATE_USER',
    resource: 'User',
    resourceId: '2',
    timestamp: new Date('2024-08-04T09:15:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 'A003',
    tenantId: 'zorgorganisatie',
    userId: '2',
    action: 'CREATE_SCHEDULE',
    resource: 'ScheduleItem',
    resourceId: 'S002',
    timestamp: new Date('2024-08-06T11:20:00'),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
];

// Helper functions
export function getClientById(id: string): Client | undefined {
  return mockClients.find(client => client.id === id);
}

export function getCareGoalsByClientId(clientId: string): CareGoal[] {
  return mockCareGoals.filter(goal => goal.clientId === clientId);
}

export function getReportsByClientId(clientId: string): Report[] {
  return mockReports.filter(report => report.clientId === clientId);
}

export function getScheduleItemsByClientId(clientId: string): ScheduleItem[] {
  return mockScheduleItems.filter(item => item.clientId === clientId);
}

export function getActiveClients(): Client[] {
  return mockClients.filter(client => client.isActive);
}

export function getActiveCareGoals(): CareGoal[] {
  return mockCareGoals.filter(goal => goal.status === 'active');
}

export function getOverdueReports(): Report[] {
  const threeDaysAgo = new Date();
  threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
  
  return mockReports.filter(report => 
    report.status === 'missing' && report.reportDate < threeDaysAgo
  );
}

export function getTodayScheduleItems(): ScheduleItem[] {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  return mockScheduleItems.filter(item => 
    item.startTime >= today && item.startTime < tomorrow
  );
}
