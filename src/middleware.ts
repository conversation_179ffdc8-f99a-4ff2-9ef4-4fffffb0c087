import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";

const JWT_SECRET =
  process.env.JWT_SECRET || "your-secret-key-change-in-production";

// Routes die authenticatie vereisen
const protectedRoutes = [
  "/dashboard",
  "/agenda",
  "/clienten",
  "/rapportages",
  "/facturatie",
  "/beheer",
];

// Routes die alleen voor admins zijn
const adminOnlyRoutes = ["/facturatie", "/beheer"];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check of de route beschermd is
  const isProtectedRoute = protectedRoutes.some((route) =>
    pathname.startsWith(route)
  );

  if (!isProtectedRoute) {
    return NextResponse.next();
  }

  // Haal token uit cookies of Authorization header
  const token =
    request.cookies.get("auth_token")?.value ||
    request.headers.get("authorization")?.replace("Bearer ", "");

  if (!token) {
    // Redirect naar login pagina
    const loginUrl = new URL("/", request.url);
    return NextResponse.redirect(loginUrl);
  }

  try {
    // Verifieer JWT token
    const payload = jwt.verify(token, JWT_SECRET) as any;

    // Check of de route admin rechten vereist
    const isAdminOnlyRoute = adminOnlyRoutes.some((route) =>
      pathname.startsWith(route)
    );

    if (isAdminOnlyRoute && payload.role !== "admin") {
      // Redirect naar dashboard als gebruiker geen admin is
      const dashboardUrl = new URL("/dashboard", request.url);
      return NextResponse.redirect(dashboardUrl);
    }

    // Voeg user info toe aan headers voor gebruik in API routes
    const response = NextResponse.next();
    response.headers.set("x-user-id", payload.userId);
    response.headers.set("x-user-email", payload.email);
    response.headers.set("x-user-role", payload.role);
    response.headers.set("x-tenant-id", payload.tenantId);

    return response;
  } catch (error) {
    // Token is ongeldig, redirect naar login
    const loginUrl = new URL("/", request.url);
    return NextResponse.redirect(loginUrl);
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
