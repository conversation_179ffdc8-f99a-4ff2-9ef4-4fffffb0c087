'use client';

import React, { useState, useEffect } from 'react';
import { 
  MagnifyingGlassIcon,
  PlusIcon,
  UserIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardContent, Button, Input, Badge } from '@/components/ui';
import { User, Tenant } from '@/types';
import { formatDateTime } from '@/utils';

interface UserManagementProps {
  onAddUser?: () => void;
}

export default function UserManagement({ onAddUser }: UserManagementProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTenant, setSelectedTenant] = useState('');
  const [showActiveOnly, setShowActiveOnly] = useState(true);

  useEffect(() => {
    fetchUsers();
    fetchTenants();
  }, [searchTerm, selectedTenant, showActiveOnly]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedTenant) params.append('tenantId', selectedTenant);
      if (showActiveOnly) params.append('active', 'true');
      if (searchTerm) params.append('search', searchTerm);

      const response = await fetch(`/api/admin/users?${params}`);
      const data = await response.json();

      if (data.success) {
        setUsers(data.data);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTenants = async () => {
    try {
      const response = await fetch('/api/admin/tenants?active=true');
      const data = await response.json();
      if (data.success) {
        setTenants(data.data);
      }
    } catch (error) {
      console.error('Error fetching tenants:', error);
    }
  };

  const getTenantName = (tenantId: string) => {
    const tenant = tenants.find(t => t.id === tenantId);
    return tenant?.name || tenantId;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Gebruikers laden...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Gebruikers Beheer</h2>
          <p className="text-gray-600 mt-1">
            Beheer gebruikers per tenant met MFA vereisten
          </p>
        </div>
        <Button onClick={onAddUser}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Nieuwe Gebruiker
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Zoek op naam of e-mail..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={selectedTenant}
              onChange={(e) => setSelectedTenant(e.target.value)}
              className="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">Alle tenants</option>
              {tenants.map((tenant) => (
                <option key={tenant.id} value={tenant.id}>
                  {tenant.name}
                </option>
              ))}
            </select>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="activeUsersOnly"
                checked={showActiveOnly}
                onChange={(e) => setShowActiveOnly(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="activeUsersOnly" className="text-sm text-gray-700">
                Alleen actieve gebruikers
              </label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {users.map((user) => (
          <Card key={user.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <div className={`p-3 rounded-lg ${user.role === 'admin' ? 'bg-purple-100' : 'bg-blue-100'}`}>
                    {user.role === 'admin' ? (
                      <ShieldCheckIcon className={`h-6 w-6 ${user.role === 'admin' ? 'text-purple-600' : 'text-blue-600'}`} />
                    ) : (
                      <UserIcon className="h-6 w-6 text-blue-600" />
                    )}
                  </div>
                  <div className="ml-4 flex-1">
                    <h3 className="font-semibold text-gray-900">{user.name}</h3>
                    <p className="text-sm text-gray-600">{user.email}</p>
                    <p className="text-sm text-gray-500 mt-1">
                      {getTenantName(user.tenantId)}
                    </p>
                    
                    <div className="flex items-center mt-2 space-x-3">
                      <Badge variant={user.role === 'admin' ? 'info' : 'default'} size="sm">
                        {user.role === 'admin' ? 'Admin' : 'Gebruiker'}
                      </Badge>
                      
                      <div className="flex items-center text-sm">
                        {user.mfaEnabled ? (
                          <CheckCircleIcon className="h-4 w-4 text-green-600 mr-1" />
                        ) : (
                          <XCircleIcon className="h-4 w-4 text-red-600 mr-1" />
                        )}
                        <span className={user.mfaEnabled ? 'text-green-600' : 'text-red-600'}>
                          MFA {user.mfaEnabled ? 'Actief' : 'Inactief'}
                        </span>
                      </div>
                    </div>

                    {user.lastLogin && (
                      <p className="text-xs text-gray-400 mt-2">
                        Laatste login: {formatDateTime(user.lastLogin)}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex flex-col items-end space-y-2">
                  <Badge variant={user.isActive ? 'success' : 'default'}>
                    {user.isActive ? 'Actief' : 'Inactief'}
                  </Badge>
                  
                  <Button variant="outline" size="sm">
                    Bewerken
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {users.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <UserIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Geen gebruikers gevonden
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || selectedTenant
                ? 'Geen gebruikers gevonden die voldoen aan uw criteria.'
                : 'Er zijn nog geen gebruikers toegevoegd.'
              }
            </p>
            {!searchTerm && !selectedTenant && (
              <Button onClick={onAddUser}>
                <PlusIcon className="h-4 w-4 mr-2" />
                Eerste Gebruiker Toevoegen
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
