'use client';

import React, { useState, useEffect } from 'react';
import { 
  CalendarDaysIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ChartBarIcon,
  ArrowDownTrayIcon,
  CurrencyEuroIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardContent, Button, Input } from '@/components/ui';
import { BillingMetrics, Tenant } from '@/types';
import { formatDate } from '@/utils';

export default function BillingOverview() {
  const [metrics, setMetrics] = useState<BillingMetrics[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState({
    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0],
  });

  useEffect(() => {
    fetchMetrics();
    fetchTenants();
  }, [selectedPeriod]);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        startDate: selectedPeriod.start,
        endDate: selectedPeriod.end,
      });

      const response = await fetch(`/api/billing/metrics?${params}`);
      const data = await response.json();

      if (data.success) {
        setMetrics(data.data);
      }
    } catch (error) {
      console.error('Error fetching billing metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTenants = async () => {
    try {
      const response = await fetch('/api/admin/tenants?active=true');
      const data = await response.json();
      if (data.success) {
        setTenants(data.data);
      }
    } catch (error) {
      console.error('Error fetching tenants:', error);
    }
  };

  const getTenantName = (tenantId: string) => {
    const tenant = tenants.find(t => t.id === tenantId);
    return tenant?.name || tenantId;
  };

  const calculateTotalMetrics = () => {
    return metrics.reduce((totals, metric) => ({
      staffCount: totals.staffCount + metric.staffCount,
      clientCount: totals.clientCount + metric.clientCount,
      reportCount: totals.reportCount + metric.reportCount,
      scheduleItemCount: totals.scheduleItemCount + metric.scheduleItemCount,
      activityCount: totals.activityCount + metric.activityCount,
    }), {
      staffCount: 0,
      clientCount: 0,
      reportCount: 0,
      scheduleItemCount: 0,
      activityCount: 0,
    });
  };

  const calculateRevenue = (metric: BillingMetrics) => {
    // Simplified pricing model
    const basePrice = 50; // €50 per staff member per month
    const activityPrice = 0.25; // €0.25 per activity
    
    return (metric.staffCount * basePrice) + (metric.activityCount * activityPrice);
  };

  const handleExportPDF = () => {
    // Mock PDF export
    const blob = new Blob(['Mock PDF content'], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `factuur-${selectedPeriod.start}-${selectedPeriod.end}.pdf`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleExportCSV = () => {
    // Mock CSV export
    const csvContent = [
      'Tenant,Medewerkers,Cliënten,Rapportages,Afspraken,Activiteiten,Omzet',
      ...metrics.map(metric => 
        `${getTenantName(metric.tenantId)},${metric.staffCount},${metric.clientCount},${metric.reportCount},${metric.scheduleItemCount},${metric.activityCount},€${calculateRevenue(metric).toFixed(2)}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `facturatie-${selectedPeriod.start}-${selectedPeriod.end}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const totals = calculateTotalMetrics();
  const totalRevenue = metrics.reduce((sum, metric) => sum + calculateRevenue(metric), 0);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Facturatie gegevens laden...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Facturatie Overzicht</h2>
          <p className="text-gray-600 mt-1">
            Automatische ratio berekeningen en export functionaliteit
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleExportCSV}>
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            CSV Export
          </Button>
          <Button onClick={handleExportPDF}>
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            PDF Export
          </Button>
        </div>
      </div>

      {/* Period Selection */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-end">
            <Input
              type="date"
              label="Startdatum"
              value={selectedPeriod.start}
              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, start: e.target.value }))}
            />
            <Input
              type="date"
              label="Einddatum"
              value={selectedPeriod.end}
              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, end: e.target.value }))}
            />
            <div className="text-sm text-gray-500">
              Periode: {formatDate(new Date(selectedPeriod.start))} - {formatDate(new Date(selectedPeriod.end))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Totaal Medewerkers</p>
                <p className="text-2xl font-bold text-gray-900">{totals.staffCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <BuildingOfficeIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Totaal Cliënten</p>
                <p className="text-2xl font-bold text-gray-900">{totals.clientCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-lg">
                <ChartBarIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Totaal Activiteiten</p>
                <p className="text-2xl font-bold text-gray-900">{totals.activityCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <CurrencyEuroIcon className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Totale Omzet</p>
                <p className="text-2xl font-bold text-gray-900">€{totalRevenue.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tenant Breakdown */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">
            Overzicht per Tenant
          </h3>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tenant
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Medewerkers
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cliënten
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rapportages
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Afspraken
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Activiteiten
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Omzet
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {metrics.map((metric) => (
                  <tr key={metric.tenantId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {getTenantName(metric.tenantId)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {metric.tenantId}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {metric.staffCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {metric.clientCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {metric.reportCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {metric.scheduleItemCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {metric.activityCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      €{calculateRevenue(metric).toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Information */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">
            Prijsmodel
          </h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Basis Tarieven</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• €50 per medewerker per maand</li>
                <li>• €0.25 per activiteit</li>
                <li>• Automatische berekening op basis van gebruik</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Activiteiten</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Rapportages aanmaken/bijwerken</li>
                <li>• Afspraken plannen/bijwerken</li>
                <li>• Cliënt acties</li>
                <li>• Systeem interacties</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
