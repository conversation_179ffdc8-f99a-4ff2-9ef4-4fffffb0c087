/* Healthcare Platform Custom Styling */

/* Custom CSS Variables for Healthcare Theme */
:root {
  /* Primary Healthcare Colors - Soft Blues */
  --healthcare-primary-50: #f0f9ff;
  --healthcare-primary-100: #e0f2fe;
  --healthcare-primary-200: #bae6fd;
  --healthcare-primary-300: #7dd3fc;
  --healthcare-primary-400: #38bdf8;
  --healthcare-primary-500: #0ea5e9;
  --healthcare-primary-600: #0284c7;
  --healthcare-primary-700: #0369a1;
  --healthcare-primary-800: #075985;
  --healthcare-primary-900: #0c4a6e;

  /* Secondary Healthcare Colors - Soft Greens */
  --healthcare-secondary-50: #f0fdf4;
  --healthcare-secondary-100: #dcfce7;
  --healthcare-secondary-200: #bbf7d0;
  --healthcare-secondary-300: #86efac;
  --healthcare-secondary-400: #4ade80;
  --healthcare-secondary-500: #22c55e;
  --healthcare-secondary-600: #16a34a;
  --healthcare-secondary-700: #15803d;
  --healthcare-secondary-800: #166534;
  --healthcare-secondary-900: #14532d;

  /* Neutral Healthcare Colors - Warm Grays */
  --healthcare-neutral-50: #fafaf9;
  --healthcare-neutral-100: #f5f5f4;
  --healthcare-neutral-200: #e7e5e4;
  --healthcare-neutral-300: #d6d3d1;
  --healthcare-neutral-400: #a8a29e;
  --healthcare-neutral-500: #78716c;
  --healthcare-neutral-600: #57534e;
  --healthcare-neutral-700: #44403c;
  --healthcare-neutral-800: #292524;
  --healthcare-neutral-900: #1c1917;

  /* Accent Colors */
  --healthcare-accent-cream: #fef7ed;
  --healthcare-accent-warm: #fed7aa;
  
  /* Status Colors */
  --healthcare-success: var(--healthcare-secondary-500);
  --healthcare-warning: #f59e0b;
  --healthcare-error: #ef4444;
  --healthcare-info: var(--healthcare-primary-500);
}

/* Base Healthcare Styling */
.healthcare-theme {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--healthcare-neutral-800);
  background-color: var(--healthcare-neutral-50);
}

/* Healthcare Button Styles */
.healthcare-btn {
  @apply inline-flex items-center justify-center rounded-xl font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.healthcare-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.healthcare-btn-primary {
  background-color: var(--healthcare-primary-600);
  color: white;
}

.healthcare-btn-primary:hover {
  background-color: var(--healthcare-primary-700);
}

.healthcare-btn-secondary {
  background-color: var(--healthcare-secondary-600);
  color: white;
}

.healthcare-btn-secondary:hover {
  background-color: var(--healthcare-secondary-700);
}

.healthcare-btn-outline {
  border: 1px solid var(--healthcare-neutral-300);
  background-color: white;
  color: var(--healthcare-neutral-700);
}

.healthcare-btn-outline:hover {
  background-color: var(--healthcare-neutral-50);
  border-color: var(--healthcare-primary-300);
}

/* Healthcare Card Styles */
.healthcare-card {
  @apply bg-white rounded-2xl shadow-sm border border-gray-200;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.healthcare-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.healthcare-card-header {
  @apply px-6 py-4 border-b border-gray-100;
  background: linear-gradient(135deg, var(--healthcare-neutral-50) 0%, white 100%);
}

.healthcare-card-content {
  @apply px-6 py-4;
}

/* Healthcare Input Styles */
.healthcare-input {
  @apply block w-full rounded-xl border border-gray-300 px-4 py-3 text-sm placeholder-gray-400 shadow-sm transition-all duration-200;
  background-color: var(--healthcare-neutral-50);
}

.healthcare-input:focus {
  border-color: var(--healthcare-primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  background-color: white;
}

/* Healthcare Navigation Styles */
.healthcare-nav {
  background: linear-gradient(180deg, white 0%, var(--healthcare-neutral-50) 100%);
  border-right: 1px solid var(--healthcare-neutral-200);
}

.healthcare-nav-item {
  @apply flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 mx-2;
}

.healthcare-nav-item:hover {
  background-color: var(--healthcare-primary-50);
  color: var(--healthcare-primary-700);
  transform: translateX(2px);
}

.healthcare-nav-item.active {
  background-color: var(--healthcare-primary-100);
  color: var(--healthcare-primary-800);
  border-left: 3px solid var(--healthcare-primary-600);
}

/* Healthcare Badge Styles */
.healthcare-badge {
  @apply inline-flex items-center font-medium rounded-full px-3 py-1 text-xs;
}

.healthcare-badge-success {
  background-color: var(--healthcare-secondary-100);
  color: var(--healthcare-secondary-800);
}

.healthcare-badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.healthcare-badge-error {
  background-color: #fee2e2;
  color: #991b1b;
}

.healthcare-badge-info {
  background-color: var(--healthcare-primary-100);
  color: var(--healthcare-primary-800);
}

/* Healthcare Modal Styles */
.healthcare-modal {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
  backdrop-filter: blur(4px);
}

.healthcare-modal-content {
  @apply bg-white rounded-2xl shadow-xl max-w-md w-full;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Healthcare Table Styles */
.healthcare-table {
  @apply min-w-full divide-y divide-gray-200;
}

.healthcare-table thead {
  background: linear-gradient(135deg, var(--healthcare-neutral-50) 0%, var(--healthcare-neutral-100) 100%);
}

.healthcare-table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.healthcare-table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.healthcare-table tbody tr:hover {
  background-color: var(--healthcare-primary-25);
}

/* Healthcare Status Indicators */
.healthcare-status-dot {
  @apply inline-block w-2 h-2 rounded-full mr-2;
}

.healthcare-status-active {
  background-color: var(--healthcare-secondary-500);
}

.healthcare-status-pending {
  background-color: var(--healthcare-warning);
}

.healthcare-status-inactive {
  background-color: var(--healthcare-neutral-400);
}

/* Healthcare Animations */
.healthcare-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.healthcare-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Healthcare Loading Spinner */
.healthcare-spinner {
  @apply inline-block w-6 h-6 border-2 border-gray-200 border-t-blue-600 rounded-full animate-spin;
}

/* Healthcare Responsive Utilities */
@media (max-width: 768px) {
  .healthcare-card {
    @apply rounded-xl;
  }
  
  .healthcare-btn {
    @apply rounded-lg;
  }
  
  .healthcare-input {
    @apply rounded-lg;
  }
}

/* Healthcare Focus Styles for Accessibility */
.healthcare-focus:focus {
  outline: 2px solid var(--healthcare-primary-500);
  outline-offset: 2px;
}

/* Healthcare Gradient Backgrounds */
.healthcare-gradient-primary {
  background: linear-gradient(135deg, var(--healthcare-primary-500) 0%, var(--healthcare-primary-600) 100%);
}

.healthcare-gradient-secondary {
  background: linear-gradient(135deg, var(--healthcare-secondary-500) 0%, var(--healthcare-secondary-600) 100%);
}

.healthcare-gradient-neutral {
  background: linear-gradient(135deg, var(--healthcare-neutral-50) 0%, white 100%);
}
