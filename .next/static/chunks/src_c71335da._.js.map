{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/utils/index.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\n\n// Utility voor het combineren van CSS classes\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n// Date formatting utilities\nexport function formatDate(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString(locale, {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function formatDateTime(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleString(locale, {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\nexport function formatTime(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleTimeString(locale, {\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Status utilities\nexport function getStatusColor(status: string): string {\n  switch (status) {\n    case 'complete':\n    case 'completed':\n    case 'active':\n      return 'text-green-600 bg-green-100';\n    case 'partial':\n    case 'paused':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'missing':\n    case 'cancelled':\n      return 'text-red-600 bg-red-100';\n    case 'scheduled':\n      return 'text-blue-600 bg-blue-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getStatusText(status: string): string {\n  switch (status) {\n    case 'complete':\n      return 'Compleet';\n    case 'partial':\n      return 'Gedeeltelijk';\n    case 'missing':\n      return 'Ontbreekt';\n    case 'completed':\n      return 'Voltooid';\n    case 'active':\n      return 'Actief';\n    case 'paused':\n      return 'Gepauzeerd';\n    case 'scheduled':\n      return 'Gepland';\n    case 'cancelled':\n      return 'Geannuleerd';\n    default:\n      return status;\n  }\n}\n\nexport function getPriorityColor(priority: string): string {\n  switch (priority) {\n    case 'high':\n      return 'text-red-600 bg-red-100';\n    case 'medium':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'low':\n      return 'text-green-600 bg-green-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getPriorityText(priority: string): string {\n  switch (priority) {\n    case 'high':\n      return 'Hoog';\n    case 'medium':\n      return 'Gemiddeld';\n    case 'low':\n      return 'Laag';\n    default:\n      return priority;\n  }\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidFQDN(email: string): boolean {\n  // Controleer of email een FQDN format heeft (<EMAIL>)\n  const fqdnRegex = /^[^\\s@]+@[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\\.[a-zA-Z]{2,}$/;\n  return fqdnRegex.test(email);\n}\n\n// Text utilities\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function capitalizeFirst(text: string): string {\n  return text.charAt(0).toUpperCase() + text.slice(1);\n}\n\n// Array utilities\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key];\n    const bVal = b[key];\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n    return 0;\n  });\n}\n\n// Local storage utilities\nexport function getFromStorage(key: string): string | null {\n  if (typeof window === 'undefined') return null;\n  try {\n    return localStorage.getItem(key);\n  } catch {\n    return null;\n  }\n}\n\nexport function setToStorage(key: string, value: string): void {\n  if (typeof window === 'undefined') return;\n  try {\n    localStorage.setItem(key, value);\n  } catch {\n    // Silently fail\n  }\n}\n\nexport function removeFromStorage(key: string): void {\n  if (typeof window === 'undefined') return;\n  try {\n    localStorage.removeItem(key);\n  } catch {\n    // Silently fail\n  }\n}\n\n// API utilities\nexport function buildQueryString(params: Record<string, any>): string {\n  const searchParams = new URLSearchParams();\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== '') {\n      if (value instanceof Date) {\n        searchParams.append(key, value.toISOString());\n      } else {\n        searchParams.append(key, String(value));\n      }\n    }\n  });\n  \n  return searchParams.toString();\n}\n\n// Error handling utilities\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'Er is een onbekende fout opgetreden';\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Generate unique ID\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAGO,SAAS,WAAW,IAAmB;QAAE,SAAA,iEAAiB;IAC/D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,QAAQ;QACxC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;QAAE,SAAA,iEAAiB;IACnE,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,cAAc,CAAC,QAAQ;QACpC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,IAAmB;QAAE,SAAA,iEAAiB;IAC/D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,QAAQ;QACxC,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,KAAa;IACvC,mEAAmE;IACnE,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;AACnD;AAGO,SAAS,QAAW,KAAU,EAAE,GAAY;IACjD,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY;QAAE,YAAA,iEAA4B;IAC9E,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,eAAe,GAAW;IACxC;;IACA,IAAI;QACF,OAAO,aAAa,OAAO,CAAC;IAC9B,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,aAAa,GAAW,EAAE,KAAa;IACrD;;IACA,IAAI;QACF,aAAa,OAAO,CAAC,KAAK;IAC5B,EAAE,UAAM;IACN,gBAAgB;IAClB;AACF;AAEO,SAAS,kBAAkB,GAAW;IAC3C;;IACA,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,UAAM;IACN,gBAAgB;IAClB;AACF;AAGO,SAAS,iBAAiB,MAA2B;IAC1D,MAAM,eAAe,IAAI;IAEzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC;YAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,iBAAiB,MAAM;gBACzB,aAAa,MAAM,CAAC,KAAK,MAAM,WAAW;YAC5C,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;IACF;IAEA,OAAO,aAAa,QAAQ;AAC9B;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/stores/authStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User, AuthState } from '@/types';\nimport { getFromStorage, setToStorage, removeFromStorage } from '@/utils';\n\ninterface AuthStore extends AuthState {\n  login: (user: User, token: string) => void;\n  logout: () => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n  updateUser: (user: Partial<User>) => void;\n}\n\nexport const useAuthStore = create<AuthStore>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      token: null,\n      isLoading: false,\n      error: null,\n\n      login: (user: User, token: string) => {\n        set({ user, token, error: null });\n        setToStorage('auth_token', token);\n      },\n\n      logout: () => {\n        set({ user: null, token: null, error: null });\n        removeFromStorage('auth_token');\n      },\n\n      setLoading: (isLoading: boolean) => {\n        set({ isLoading });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n      },\n\n      clearError: () => {\n        set({ error: null });\n      },\n\n      updateUser: (userData: Partial<User>) => {\n        const currentUser = get().user;\n        if (currentUser) {\n          set({ user: { ...currentUser, ...userData } });\n        }\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAWO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,OAAO;QACP,WAAW;QACX,OAAO;QAEP,OAAO,CAAC,MAAY;YAClB,IAAI;gBAAE;gBAAM;gBAAO,OAAO;YAAK;YAC/B,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,cAAc;QAC7B;QAEA,QAAQ;YACN,IAAI;gBAAE,MAAM;gBAAM,OAAO;gBAAM,OAAO;YAAK;YAC3C,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EAAE;QACpB;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;YAAU;QAClB;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,YAAY,CAAC;YACX,MAAM,cAAc,MAAM,IAAI;YAC9B,IAAI,aAAa;gBACf,IAAI;oBAAE,MAAM;wBAAE,GAAG,WAAW;wBAAE,GAAG,QAAQ;oBAAC;gBAAE;YAC9C;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;QACpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Button.tsx"], "sourcesContent": ["import React from \"react\";\nimport { cn } from \"@/utils\";\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"primary\" | \"secondary\" | \"outline\" | \"ghost\" | \"danger\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  (\n    {\n      className,\n      variant = \"primary\",\n      size = \"md\",\n      loading = false,\n      disabled,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = \"healthcare-btn healthcare-focus\";\n\n    const variants = {\n      primary: \"healthcare-btn-primary\",\n      secondary: \"healthcare-btn-secondary\",\n      outline: \"healthcare-btn-outline\",\n      ghost:\n        \"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 bg-transparent border-transparent rounded-xl\",\n      danger:\n        \"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 rounded-xl\",\n    };\n\n    const sizes = {\n      sm: \"px-4 py-2 text-sm\",\n      md: \"px-6 py-3 text-sm\",\n      lg: \"px-8 py-4 text-base\",\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAUE;QATA,EACE,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IAGD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OACE;QACF,QACE;IACJ;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Input.tsx"], "sourcesContent": ["import React from \"react\";\nimport { cn } from \"@/utils\";\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, label, error, helperText, id, ...props }, ref) => {\n    const generatedId = React.useId();\n    const inputId = id || generatedId;\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <input\n          ref={ref}\n          id={inputId}\n          className={cn(\n            \"healthcare-input healthcare-focus\",\n            \"disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed\",\n            error && \"border-red-300 focus:border-red-500 focus:ring-red-500\",\n            className\n          )}\n          {...props}\n        />\n        {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = \"Input\";\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAQA,MAAM,sBAAQ,GAAA,6JAAA,CAAA,UAAK,CAAC,UAAU,SAC5B,QAAwD;QAAvD,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,OAAO;;IACpD,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,KAAK;IAC/B,MAAM,UAAU,MAAM;IAEtB,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,qCACA,0EACA,SAAS,0DACT;gBAED,GAAG,KAAK;;;;;;YAEV,uBAAS,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YACnD,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Card.tsx"], "sourcesContent": ["import React from \"react\";\nimport { cn } from \"@/utils\";\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"healthcare-card healthcare-fade-in\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"healthcare-card-header\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"healthcare-card-content\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"px-6 py-4 border-t border-gray-200\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nCard.displayName = \"Card\";\nCardHeader.displayName = \"CardHeader\";\nCardContent.displayName = \"CardContent\";\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAkBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;;;AAKP,KAAK,WAAW,GAAG;AACnB,WAAW,WAAW,GAAG;AACzB,YAAY,WAAW,GAAG;AAC1B,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from \"react\";\nimport { cn } from \"@/utils\";\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {\n  variant?: \"default\" | \"success\" | \"warning\" | \"danger\" | \"info\";\n  size?: \"sm\" | \"md\";\n  children: React.ReactNode;\n}\n\nconst Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(\n  (\n    { className, variant = \"default\", size = \"md\", children, ...props },\n    ref\n  ) => {\n    const baseClasses = \"healthcare-badge\";\n\n    const variants = {\n      default: \"bg-gray-100 text-gray-800\",\n      success: \"healthcare-badge-success\",\n      warning: \"healthcare-badge-warning\",\n      danger: \"healthcare-badge-error\",\n      info: \"healthcare-badge-info\",\n    };\n\n    const sizes = {\n      sm: \"px-3 py-1 text-xs\",\n      md: \"px-4 py-1.5 text-sm\",\n    };\n\n    return (\n      <span\n        ref={ref}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        {...props}\n      >\n        {children}\n      </span>\n    );\n  }\n);\n\nBadge.displayName = \"Badge\";\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,QAEE;QADA,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;IAGnE,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC1D,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/index.ts"], "sourcesContent": ["export { default as But<PERSON> } from './Button';\nexport { default as Input } from './Input';\nexport { Card, CardHeader, CardContent, CardFooter } from './Card';\nexport { default as Badge } from './Badge';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/stores/appStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { AppState, Tenant, Notification } from '@/types';\nimport { generateId } from '@/utils';\n\ninterface AppStore extends AppState {\n  setSidebarOpen: (open: boolean) => void;\n  toggleSidebar: () => void;\n  setCurrentTenant: (tenant: Tenant | null) => void;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationAsRead: (id: string) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n}\n\nexport const useAppStore = create<AppStore>((set, get) => ({\n  sidebarOpen: false,\n  currentTenant: null,\n  notifications: [],\n\n  setSidebarOpen: (sidebarOpen: boolean) => {\n    set({ sidebarOpen });\n  },\n\n  toggleSidebar: () => {\n    set((state) => ({ sidebarOpen: !state.sidebarOpen }));\n  },\n\n  setCurrentTenant: (currentTenant: Tenant | null) => {\n    set({ currentTenant });\n  },\n\n  addNotification: (notificationData) => {\n    const notification: Notification = {\n      ...notificationData,\n      id: generateId(),\n      timestamp: new Date(),\n      read: false,\n    };\n\n    set((state) => ({\n      notifications: [notification, ...state.notifications],\n    }));\n\n    // Auto-remove notification after 5 seconds for non-error types\n    if (notificationData.type !== 'error') {\n      setTimeout(() => {\n        get().removeNotification(notification.id);\n      }, 5000);\n    }\n  },\n\n  markNotificationAsRead: (id: string) => {\n    set((state) => ({\n      notifications: state.notifications.map((notification) =>\n        notification.id === id ? { ...notification, read: true } : notification\n      ),\n    }));\n  },\n\n  removeNotification: (id: string) => {\n    set((state) => ({\n      notifications: state.notifications.filter((notification) => notification.id !== id),\n    }));\n  },\n\n  clearNotifications: () => {\n    set({ notifications: [] });\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAYO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAY,CAAC,KAAK,MAAQ,CAAC;QACzD,aAAa;QACb,eAAe;QACf,eAAe,EAAE;QAEjB,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;QACpB;QAEA,eAAe;YACb,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QACrD;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE;YAAc;QACtB;QAEA,iBAAiB,CAAC;YAChB,MAAM,eAA6B;gBACjC,GAAG,gBAAgB;gBACnB,IAAI,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD;gBACb,WAAW,IAAI;gBACf,MAAM;YACR;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBAAC;2BAAiB,MAAM,aAAa;qBAAC;gBACvD,CAAC;YAED,+DAA+D;YAC/D,IAAI,iBAAiB,IAAI,KAAK,SAAS;gBACrC,WAAW;oBACT,MAAM,kBAAkB,CAAC,aAAa,EAAE;gBAC1C,GAAG;YACL;QACF;QAEA,wBAAwB,CAAC;YACvB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eACtC,aAAa,EAAE,KAAK,KAAK;4BAAE,GAAG,YAAY;4BAAE,MAAM;wBAAK,IAAI;gBAE/D,CAAC;QACH;QAEA,oBAAoB,CAAC;YACnB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,eAAiB,aAAa,EAAE,KAAK;gBAClF,CAAC;QACH;QAEA,oBAAoB;YAClB,IAAI;gBAAE,eAAe,EAAE;YAAC;QAC1B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/hooks/useAuth.ts"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/stores/authStore';\nimport { useAppStore } from '@/stores/appStore';\nimport { getFromStorage } from '@/utils';\n\nexport function useAuth() {\n  const router = useRouter();\n  const { user, token, login, logout, setLoading, setError } = useAuthStore();\n  const { addNotification } = useAppStore();\n\n  // Check voor bestaande sessie bij app start\n  useEffect(() => {\n    const checkExistingSession = async () => {\n      const storedToken = getFromStorage('auth_token');\n      \n      if (storedToken && !user) {\n        try {\n          setLoading(true);\n          \n          // Verifieer token met backend\n          const response = await fetch('/api/auth/verify', {\n            headers: {\n              'Authorization': `Bearer ${storedToken}`,\n            },\n          });\n\n          if (response.ok) {\n            const { user: userData } = await response.json();\n            login(userData, storedToken);\n          } else {\n            // Token is ongeldig, verwijder uit storage\n            logout();\n          }\n        } catch (error) {\n          console.error('Session verification failed:', error);\n          logout();\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n\n    checkExistingSession();\n  }, [user, login, logout, setLoading]);\n\n  const handleLogin = async (email: string, password: string, mfaCode?: string) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password, mfaCode }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Inloggen mislukt');\n      }\n\n      login(data.user, data.token);\n      \n      // Set cookie voor middleware\n      document.cookie = `auth_token=${data.token}; path=/; max-age=${8 * 60 * 60}`; // 8 uur\n\n      addNotification({\n        type: 'success',\n        title: 'Welkom terug!',\n        message: `Succesvol ingelogd als ${data.user.name}`,\n      });\n\n      return { success: true, requiresMFA: false };\n\n    } catch (error: any) {\n      const message = error.message || 'Er is een fout opgetreden';\n      setError(message);\n      \n      addNotification({\n        type: 'error',\n        title: 'Inloggen mislukt',\n        message,\n      });\n\n      // Check voor MFA requirement\n      if (error.message?.includes('MFA') || message.includes('MFA')) {\n        return { success: false, requiresMFA: true };\n      }\n\n      return { success: false, requiresMFA: false };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    logout();\n    \n    // Verwijder cookie\n    document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';\n    \n    addNotification({\n      type: 'info',\n      title: 'Uitgelogd',\n      message: 'U bent succesvol uitgelogd',\n    });\n\n    router.push('/');\n  };\n\n  const isAdmin = user?.role === 'admin';\n  const isAuthenticated = !!user && !!token;\n\n  return {\n    user,\n    token,\n    isAuthenticated,\n    isAdmin,\n    login: handleLogin,\n    logout: handleLogout,\n    loading: useAuthStore(state => state.isLoading),\n    error: useAuthStore(state => state.error),\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IACxE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEtC,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;0DAAuB;oBAC3B,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;oBAEnC,IAAI,eAAe,CAAC,MAAM;wBACxB,IAAI;4BACF,WAAW;4BAEX,8BAA8B;4BAC9B,MAAM,WAAW,MAAM,MAAM,oBAAoB;gCAC/C,SAAS;oCACP,iBAAiB,AAAC,UAAqB,OAAZ;gCAC7B;4BACF;4BAEA,IAAI,SAAS,EAAE,EAAE;gCACf,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAAS,IAAI;gCAC9C,MAAM,UAAU;4BAClB,OAAO;gCACL,2CAA2C;gCAC3C;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,gCAAgC;4BAC9C;wBACF,SAAU;4BACR,WAAW;wBACb;oBACF;gBACF;;YAEA;QACF;4BAAG;QAAC;QAAM;QAAO;QAAQ;KAAW;IAEpC,MAAM,cAAc,OAAO,OAAe,UAAkB;QAC1D,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;oBAAU;gBAAQ;YAClD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,MAAM,KAAK,IAAI,EAAE,KAAK,KAAK;YAE3B,6BAA6B;YAC7B,SAAS,MAAM,GAAG,AAAC,cAA4C,OAA/B,KAAK,KAAK,EAAC,sBAAgC,OAAZ,IAAI,KAAK,KAAM,QAAQ;YAEtF,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS,AAAC,0BAAwC,OAAf,KAAK,IAAI,CAAC,IAAI;YACnD;YAEA,OAAO;gBAAE,SAAS;gBAAM,aAAa;YAAM;QAE7C,EAAE,OAAO,OAAY;gBAWf;YAVJ,MAAM,UAAU,MAAM,OAAO,IAAI;YACjC,SAAS;YAET,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP;YACF;YAEA,6BAA6B;YAC7B,IAAI,EAAA,iBAAA,MAAM,OAAO,cAAb,qCAAA,eAAe,QAAQ,CAAC,WAAU,QAAQ,QAAQ,CAAC,QAAQ;gBAC7D,OAAO;oBAAE,SAAS;oBAAO,aAAa;gBAAK;YAC7C;YAEA,OAAO;gBAAE,SAAS;gBAAO,aAAa;YAAM;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB;QAEA,mBAAmB;QACnB,SAAS,MAAM,GAAG;QAElB,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;QACX;QAEA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,UAAU,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAC/B,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC;IAEpC,OAAO;QACL;QACA;QACA;QACA;QACA,OAAO;QACP,QAAQ;QACR,SAAS,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;oCAAE,CAAA,QAAS,MAAM,SAAS;;QAC9C,OAAO,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;oCAAE,CAAA,QAAS,MAAM,KAAK;;IAC1C;AACF;GAzHgB;;QACC,qIAAA,CAAA,YAAS;QACqC,6HAAA,CAAA,eAAY;QAC7C,4HAAA,CAAA,cAAW;QAmH5B,6HAAA,CAAA,eAAY;QACd,6HAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/auth/LoginForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { Button, Input, Card, CardHeader, CardContent } from \"@/components/ui\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { LoginForm as LoginFormType } from \"@/types\";\nimport { isValidFQDN } from \"@/utils\";\n\nconst loginSchema = z.object({\n  email: z\n    .string()\n    .min(1, \"E-mailadres is verplicht\")\n    .refine(\n      isValidFQDN,\n      \"Voer een geldig e-mailadres in (<EMAIL>)\"\n    ),\n  password: z\n    .string()\n    .min(1, \"Wachtwoord is verplicht\")\n    .min(8, \"Wachtwoord moet minimaal 8 karakters bevatten\"),\n  mfaCode: z\n    .string()\n    .optional()\n    .refine(\n      (val) => !val || val.length === 6,\n      \"MFA code moet 6 cijfers bevatten\"\n    ),\n});\n\ninterface LoginFormProps {\n  onSuccess?: () => void;\n}\n\nexport default function LoginForm({ onSuccess }: LoginFormProps) {\n  const [showMFA, setShowMFA] = useState(false);\n  const { login, loading, error } = useAuth();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    setError: setFormError,\n  } = useForm<LoginFormType>({\n    resolver: zodResolver(loginSchema),\n  });\n\n  const onSubmit = async (data: LoginFormType) => {\n    const result = await login(data.email, data.password, data.mfaCode);\n\n    if (result.requiresMFA && !showMFA) {\n      setShowMFA(true);\n      return;\n    }\n\n    if (result.success) {\n      onSuccess?.();\n    } else if (error) {\n      setFormError(\"root\", { message: error });\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full\">\n        <Card>\n          <CardHeader>\n            <div className=\"text-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                ZorgPortaal Plus\n              </h1>\n              <p className=\"text-gray-600\">Log in op uw account</p>\n            </div>\n          </CardHeader>\n\n          <CardContent>\n            <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n              <Input\n                {...register(\"email\")}\n                type=\"email\"\n                label=\"E-mailadres\"\n                placeholder=\"<EMAIL>\"\n                error={errors.email?.message}\n                autoComplete=\"email\"\n              />\n\n              <Input\n                {...register(\"password\")}\n                type=\"password\"\n                label=\"Wachtwoord\"\n                placeholder=\"Voer uw wachtwoord in\"\n                error={errors.password?.message}\n                autoComplete=\"current-password\"\n              />\n\n              {showMFA && (\n                <Input\n                  {...register(\"mfaCode\")}\n                  type=\"text\"\n                  label=\"MFA Code\"\n                  placeholder=\"123456\"\n                  error={errors.mfaCode?.message}\n                  maxLength={6}\n                  autoComplete=\"one-time-code\"\n                />\n              )}\n\n              {errors.root && (\n                <div className=\"text-sm text-red-600 bg-red-50 p-3 rounded-lg\">\n                  {errors.root.message}\n                </div>\n              )}\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                loading={loading}\n                disabled={loading}\n              >\n                {showMFA ? \"Verifiëren\" : \"Inloggen\"}\n              </Button>\n            </form>\n\n            <div className=\"mt-6 text-center\">\n              <p className=\"text-sm text-gray-500\">\n                Problemen met inloggen?{\" \"}\n                <a href=\"#\" className=\"text-blue-600 hover:text-blue-500\">\n                  Neem contact op met uw beheerder\n                </a>\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;;;AATA;;;;;;;;AAWA,MAAM,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,gLAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,4BACP,MAAM,CACL,wHAAA,CAAA,cAAW,EACX;IAEJ,UAAU,gLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,2BACP,GAAG,CAAC,GAAG;IACV,SAAS,gLAAA,CAAA,IAAC,CACP,MAAM,GACN,QAAQ,GACR,MAAM,CACL,CAAC,MAAQ,CAAC,OAAO,IAAI,MAAM,KAAK,GAChC;AAEN;AAMe,SAAS,UAAU,KAA6B;QAA7B,EAAE,SAAS,EAAkB,GAA7B;QAgDX,eASA,kBAUE;;IAlEvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAExC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,UAAU,YAAY,EACvB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,SAAS,MAAM,MAAM,KAAK,KAAK,EAAE,KAAK,QAAQ,EAAE,KAAK,OAAO;QAElE,IAAI,OAAO,WAAW,IAAI,CAAC,SAAS;YAClC,WAAW;YACX;QACF;QAEA,IAAI,OAAO,OAAO,EAAE;YAClB,sBAAA,gCAAA;QACF,OAAO,IAAI,OAAO;YAChB,aAAa,QAAQ;gBAAE,SAAS;YAAM;QACxC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAIjC,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAK,UAAU,aAAa;gCAAW,WAAU;;kDAChD,6LAAC,wKAAA,CAAA,QAAK;wCACH,GAAG,SAAS,QAAQ;wCACrB,MAAK;wCACL,OAAM;wCACN,aAAY;wCACZ,KAAK,GAAE,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,OAAO;wCAC5B,cAAa;;;;;;kDAGf,6LAAC,wKAAA,CAAA,QAAK;wCACH,GAAG,SAAS,WAAW;wCACxB,MAAK;wCACL,OAAM;wCACN,aAAY;wCACZ,KAAK,GAAE,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,OAAO;wCAC/B,cAAa;;;;;;oCAGd,yBACC,6LAAC,wKAAA,CAAA,QAAK;wCACH,GAAG,SAAS,UAAU;wCACvB,MAAK;wCACL,OAAM;wCACN,aAAY;wCACZ,KAAK,GAAE,kBAAA,OAAO,OAAO,cAAd,sCAAA,gBAAgB,OAAO;wCAC9B,WAAW;wCACX,cAAa;;;;;;oCAIhB,OAAO,IAAI,kBACV,6LAAC;wCAAI,WAAU;kDACZ,OAAO,IAAI,CAAC,OAAO;;;;;;kDAIxB,6LAAC,0KAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,SAAS;wCACT,UAAU;kDAET,UAAU,eAAe;;;;;;;;;;;;0CAI9B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;wCAAwB;wCACX;sDACxB,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1E;GAtGwB;;QAEY,0HAAA,CAAA,UAAO;QAOrC,iKAAA,CAAA,UAAO;;;KATW", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { useAuthStore } from \"@/stores/authStore\";\nimport LoginForm from \"@/components/auth/LoginForm\";\n\nexport default function Home() {\n  const router = useRouter();\n  const { user } = useAuthStore();\n\n  useEffect(() => {\n    if (user) {\n      router.push(\"/dashboard\");\n    }\n  }, [user, router]);\n\n  if (user) {\n    return null; // Redirect wordt afgehandeld door useEffect\n  }\n\n  return <LoginForm onSuccess={() => router.push(\"/dashboard\")} />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,MAAM;gBACR,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAM;KAAO;IAEjB,IAAI,MAAM;QACR,OAAO,MAAM,4CAA4C;IAC3D;IAEA,qBAAO,6LAAC,0IAAA,CAAA,UAAS;QAAC,WAAW,IAAM,OAAO,IAAI,CAAC;;;;;;AACjD;GAfwB;;QACP,qIAAA,CAAA,YAAS;QACP,6HAAA,CAAA,eAAY;;;KAFP", "debugId": null}}]}