{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/utils/index.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\n\n// Utility voor het combineren van CSS classes\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n// Date formatting utilities\nexport function formatDate(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString(locale, {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function formatDateTime(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleString(locale, {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\nexport function formatTime(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleTimeString(locale, {\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Status utilities\nexport function getStatusColor(status: string): string {\n  switch (status) {\n    case 'complete':\n    case 'completed':\n    case 'active':\n      return 'text-green-600 bg-green-100';\n    case 'partial':\n    case 'paused':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'missing':\n    case 'cancelled':\n      return 'text-red-600 bg-red-100';\n    case 'scheduled':\n      return 'text-blue-600 bg-blue-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getStatusText(status: string): string {\n  switch (status) {\n    case 'complete':\n      return 'Compleet';\n    case 'partial':\n      return 'Gedeeltelijk';\n    case 'missing':\n      return 'Ontbreekt';\n    case 'completed':\n      return 'Voltooid';\n    case 'active':\n      return 'Actief';\n    case 'paused':\n      return 'Gepauzeerd';\n    case 'scheduled':\n      return 'Gepland';\n    case 'cancelled':\n      return 'Geannuleerd';\n    default:\n      return status;\n  }\n}\n\nexport function getPriorityColor(priority: string): string {\n  switch (priority) {\n    case 'high':\n      return 'text-red-600 bg-red-100';\n    case 'medium':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'low':\n      return 'text-green-600 bg-green-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getPriorityText(priority: string): string {\n  switch (priority) {\n    case 'high':\n      return 'Hoog';\n    case 'medium':\n      return 'Gemiddeld';\n    case 'low':\n      return 'Laag';\n    default:\n      return priority;\n  }\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidFQDN(email: string): boolean {\n  // Controleer of email een FQDN format heeft (<EMAIL>)\n  const fqdnRegex = /^[^\\s@]+@[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\\.[a-zA-Z]{2,}$/;\n  return fqdnRegex.test(email);\n}\n\n// Text utilities\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function capitalizeFirst(text: string): string {\n  return text.charAt(0).toUpperCase() + text.slice(1);\n}\n\n// Array utilities\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key];\n    const bVal = b[key];\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n    return 0;\n  });\n}\n\n// Local storage utilities\nexport function getFromStorage(key: string): string | null {\n  if (typeof window === 'undefined') return null;\n  try {\n    return localStorage.getItem(key);\n  } catch {\n    return null;\n  }\n}\n\nexport function setToStorage(key: string, value: string): void {\n  if (typeof window === 'undefined') return;\n  try {\n    localStorage.setItem(key, value);\n  } catch {\n    // Silently fail\n  }\n}\n\nexport function removeFromStorage(key: string): void {\n  if (typeof window === 'undefined') return;\n  try {\n    localStorage.removeItem(key);\n  } catch {\n    // Silently fail\n  }\n}\n\n// API utilities\nexport function buildQueryString(params: Record<string, any>): string {\n  const searchParams = new URLSearchParams();\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== '') {\n      if (value instanceof Date) {\n        searchParams.append(key, value.toISOString());\n      } else {\n        searchParams.append(key, String(value));\n      }\n    }\n  });\n  \n  return searchParams.toString();\n}\n\n// Error handling utilities\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'Er is een onbekende fout opgetreden';\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Generate unique ID\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAGO,SAAS,WAAW,IAAmB;QAAE,SAAA,iEAAiB;IAC/D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,QAAQ;QACxC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;QAAE,SAAA,iEAAiB;IACnE,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,cAAc,CAAC,QAAQ;QACpC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,IAAmB;QAAE,SAAA,iEAAiB;IAC/D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,QAAQ;QACxC,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,KAAa;IACvC,mEAAmE;IACnE,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;AACnD;AAGO,SAAS,QAAW,KAAU,EAAE,GAAY;IACjD,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY;QAAE,YAAA,iEAA4B;IAC9E,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,eAAe,GAAW;IACxC;;IACA,IAAI;QACF,OAAO,aAAa,OAAO,CAAC;IAC9B,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,aAAa,GAAW,EAAE,KAAa;IACrD;;IACA,IAAI;QACF,aAAa,OAAO,CAAC,KAAK;IAC5B,EAAE,UAAM;IACN,gBAAgB;IAClB;AACF;AAEO,SAAS,kBAAkB,GAAW;IAC3C;;IACA,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,UAAM;IACN,gBAAgB;IAClB;AACF;AAGO,SAAS,iBAAiB,MAA2B;IAC1D,MAAM,eAAe,IAAI;IAEzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC;YAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,iBAAiB,MAAM;gBACzB,aAAa,MAAM,CAAC,KAAK,MAAM,WAAW;YAC5C,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;IACF;IAEA,OAAO,aAAa,QAAQ;AAC9B;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/stores/authStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User, AuthState } from '@/types';\nimport { getFromStorage, setToStorage, removeFromStorage } from '@/utils';\n\ninterface AuthStore extends AuthState {\n  login: (user: User, token: string) => void;\n  logout: () => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n  updateUser: (user: Partial<User>) => void;\n}\n\nexport const useAuthStore = create<AuthStore>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      token: null,\n      isLoading: false,\n      error: null,\n\n      login: (user: User, token: string) => {\n        set({ user, token, error: null });\n        setToStorage('auth_token', token);\n      },\n\n      logout: () => {\n        set({ user: null, token: null, error: null });\n        removeFromStorage('auth_token');\n      },\n\n      setLoading: (isLoading: boolean) => {\n        set({ isLoading });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n      },\n\n      clearError: () => {\n        set({ error: null });\n      },\n\n      updateUser: (userData: Partial<User>) => {\n        const currentUser = get().user;\n        if (currentUser) {\n          set({ user: { ...currentUser, ...userData } });\n        }\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAWO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,OAAO;QACP,WAAW;QACX,OAAO;QAEP,OAAO,CAAC,MAAY;YAClB,IAAI;gBAAE;gBAAM;gBAAO,OAAO;YAAK;YAC/B,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,cAAc;QAC7B;QAEA,QAAQ;YACN,IAAI;gBAAE,MAAM;gBAAM,OAAO;gBAAM,OAAO;YAAK;YAC3C,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EAAE;QACpB;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;YAAU;QAClB;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,YAAY,CAAC;YACX,MAAM,cAAc,MAAM,IAAI;YAC9B,IAAI,aAAa;gBACf,IAAI;oBAAE,MAAM;wBAAE,GAAG,WAAW;wBAAE,GAAG,QAAQ;oBAAC;gBAAE;YAC9C;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;QACpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/stores/appStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { AppState, Tenant, Notification } from '@/types';\nimport { generateId } from '@/utils';\n\ninterface AppStore extends AppState {\n  setSidebarOpen: (open: boolean) => void;\n  toggleSidebar: () => void;\n  setCurrentTenant: (tenant: Tenant | null) => void;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationAsRead: (id: string) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n}\n\nexport const useAppStore = create<AppStore>((set, get) => ({\n  sidebarOpen: false,\n  currentTenant: null,\n  notifications: [],\n\n  setSidebarOpen: (sidebarOpen: boolean) => {\n    set({ sidebarOpen });\n  },\n\n  toggleSidebar: () => {\n    set((state) => ({ sidebarOpen: !state.sidebarOpen }));\n  },\n\n  setCurrentTenant: (currentTenant: Tenant | null) => {\n    set({ currentTenant });\n  },\n\n  addNotification: (notificationData) => {\n    const notification: Notification = {\n      ...notificationData,\n      id: generateId(),\n      timestamp: new Date(),\n      read: false,\n    };\n\n    set((state) => ({\n      notifications: [notification, ...state.notifications],\n    }));\n\n    // Auto-remove notification after 5 seconds for non-error types\n    if (notificationData.type !== 'error') {\n      setTimeout(() => {\n        get().removeNotification(notification.id);\n      }, 5000);\n    }\n  },\n\n  markNotificationAsRead: (id: string) => {\n    set((state) => ({\n      notifications: state.notifications.map((notification) =>\n        notification.id === id ? { ...notification, read: true } : notification\n      ),\n    }));\n  },\n\n  removeNotification: (id: string) => {\n    set((state) => ({\n      notifications: state.notifications.filter((notification) => notification.id !== id),\n    }));\n  },\n\n  clearNotifications: () => {\n    set({ notifications: [] });\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAYO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAY,CAAC,KAAK,MAAQ,CAAC;QACzD,aAAa;QACb,eAAe;QACf,eAAe,EAAE;QAEjB,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;QACpB;QAEA,eAAe;YACb,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QACrD;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE;YAAc;QACtB;QAEA,iBAAiB,CAAC;YAChB,MAAM,eAA6B;gBACjC,GAAG,gBAAgB;gBACnB,IAAI,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD;gBACb,WAAW,IAAI;gBACf,MAAM;YACR;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBAAC;2BAAiB,MAAM,aAAa;qBAAC;gBACvD,CAAC;YAED,+DAA+D;YAC/D,IAAI,iBAAiB,IAAI,KAAK,SAAS;gBACrC,WAAW;oBACT,MAAM,kBAAkB,CAAC,aAAa,EAAE;gBAC1C,GAAG;YACL;QACF;QAEA,wBAAwB,CAAC;YACvB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eACtC,aAAa,EAAE,KAAK,KAAK;4BAAE,GAAG,YAAY;4BAAE,MAAM;wBAAK,IAAI;gBAE/D,CAAC;QACH;QAEA,oBAAoB,CAAC;YACnB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,eAAiB,aAAa,EAAE,KAAK;gBAClF,CAAC;QACH;QAEA,oBAAoB;YAClB,IAAI;gBAAE,eAAe,EAAE;YAAC;QAC1B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  CalendarDaysIcon,\n  UserGroupIcon,\n  DocumentTextIcon,\n  CreditCardIcon,\n  CogIcon,\n  HomeIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { cn } from '@/utils';\nimport { useAuthStore } from '@/stores/authStore';\nimport { useAppStore } from '@/stores/appStore';\nimport { NavItem } from '@/types';\n\nconst navigationItems: NavItem[] = [\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: HomeIcon,\n  },\n  {\n    name: 'Agenda',\n    href: '/agenda',\n    icon: CalendarDaysIcon,\n  },\n  {\n    name: '<PERSON><PERSON><PERSON>nten',\n    href: '/clienten',\n    icon: UserGroupIcon,\n  },\n  {\n    name: 'Rapportages',\n    href: '/rapportages',\n    icon: DocumentTextIcon,\n  },\n  {\n    name: 'Facturatie',\n    href: '/facturatie',\n    icon: CreditCardIcon,\n    adminOnly: true,\n  },\n  {\n    name: 'Beheer',\n    href: '/beheer',\n    icon: CogIcon,\n    adminOnly: true,\n  },\n];\n\ninterface NavigationProps {\n  className?: string;\n}\n\nexport default function Navigation({ className }: NavigationProps) {\n  const pathname = usePathname();\n  const { user } = useAuthStore();\n  const { sidebarOpen, setSidebarOpen } = useAppStore();\n\n  const filteredItems = navigationItems.filter(item => \n    !item.adminOnly || user?.role === 'admin'\n  );\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <button\n          onClick={() => setSidebarOpen(!sidebarOpen)}\n          className=\"p-2 rounded-lg bg-white shadow-md border border-gray-200\"\n        >\n          {sidebarOpen ? (\n            <XMarkIcon className=\"h-6 w-6 text-gray-600\" />\n          ) : (\n            <Bars3Icon className=\"h-6 w-6 text-gray-600\" />\n          )}\n        </button>\n      </div>\n\n      {/* Mobile overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <nav className={cn(\n        'fixed top-0 left-0 h-full bg-white border-r border-gray-200 shadow-sm z-40 transition-transform duration-300',\n        'lg:translate-x-0 lg:static lg:z-auto',\n        sidebarOpen ? 'translate-x-0' : '-translate-x-full',\n        'w-64',\n        className\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"p-6 border-b border-gray-200\">\n            <h1 className=\"text-xl font-bold text-gray-900\">\n              ZorgPortaal Plus\n            </h1>\n            {user && (\n              <p className=\"text-sm text-gray-500 mt-1\">\n                {user.name}\n              </p>\n            )}\n          </div>\n\n          {/* Navigation items */}\n          <div className=\"flex-1 px-4 py-6 space-y-2\">\n            {filteredItems.map((item) => {\n              const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n              \n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setSidebarOpen(false)}\n                  className={cn(\n                    'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                  )}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* User info and logout */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"text-xs text-gray-500 mb-2\">\n              Tenant: {user?.tenantId}\n            </div>\n            <button\n              onClick={() => {\n                useAuthStore.getState().logout();\n                setSidebarOpen(false);\n              }}\n              className=\"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              Uitloggen\n            </button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Bottom navigation for mobile */}\n      <div className=\"lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-30\">\n        <div className=\"flex\">\n          {filteredItems.slice(0, 4).map((item) => {\n            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n            \n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  'flex-1 flex flex-col items-center py-2 px-1 text-xs',\n                  isActive\n                    ? 'text-blue-700'\n                    : 'text-gray-500'\n                )}\n              >\n                <item.icon className=\"h-5 w-5 mb-1\" />\n                <span className=\"truncate\">{item.name}</span>\n              </Link>\n            );\n          })}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;AAoBA,MAAM,kBAA6B;IACjC;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kOAAA,CAAA,mBAAgB;IACxB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,4NAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kOAAA,CAAA,mBAAgB;IACxB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,8NAAA,CAAA,iBAAc;QACpB,WAAW;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gNAAA,CAAA,UAAO;QACb,WAAW;IACb;CACD;AAMc,SAAS,WAAW,KAA8B;QAA9B,EAAE,SAAS,EAAmB,GAA9B;;IACjC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAElD,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,OAC3C,CAAC,KAAK,SAAS,IAAI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAGpC,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS,IAAM,eAAe,CAAC;oBAC/B,WAAU;8BAET,4BACC,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;6CAErB,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAM1B,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACf,gHACA,wCACA,cAAc,kBAAkB,qBAChC,QACA;0BAEA,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;gCAG/C,sBACC,6LAAC;oCAAE,WAAU;8CACV,KAAK,IAAI;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC;gCAClB,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;gCAE3E,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,eAAe;oCAC9B,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,wDACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCAXL,KAAK,IAAI;;;;;4BAcpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAA6B;wCACjC,iBAAA,2BAAA,KAAM,QAAQ;;;;;;;8CAEzB,6LAAC;oCACC,SAAS;wCACP,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;wCAC9B,eAAe;oCACjB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wBAC9B,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;wBAE3E,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,uDACA,WACI,kBACA;;8CAGN,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAK,WAAU;8CAAY,KAAK,IAAI;;;;;;;2BAVhC,KAAK,IAAI;;;;;oBAapB;;;;;;;;;;;;;AAKV;GA1HwB;;QACL,qIAAA,CAAA,cAAW;QACX,6HAAA,CAAA,eAAY;QACW,4HAAA,CAAA,cAAW;;;KAH7B", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/layout/NotificationToast.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { \n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  InformationCircleIcon,\n  XCircleIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { useAppStore } from '@/stores/appStore';\nimport { cn } from '@/utils';\n\nconst iconMap = {\n  success: CheckCircleIcon,\n  warning: ExclamationTriangleIcon,\n  info: InformationCircleIcon,\n  error: XCircleIcon,\n};\n\nconst colorMap = {\n  success: 'bg-green-50 border-green-200 text-green-800',\n  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n  info: 'bg-blue-50 border-blue-200 text-blue-800',\n  error: 'bg-red-50 border-red-200 text-red-800',\n};\n\nexport default function NotificationToast() {\n  const { notifications, removeNotification } = useAppStore();\n\n  if (notifications.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {notifications.map((notification) => {\n        const Icon = iconMap[notification.type];\n        \n        return (\n          <div\n            key={notification.id}\n            className={cn(\n              'p-4 rounded-lg border shadow-lg transition-all duration-300',\n              colorMap[notification.type]\n            )}\n          >\n            <div className=\"flex items-start\">\n              <Icon className=\"h-5 w-5 mt-0.5 mr-3 flex-shrink-0\" />\n              <div className=\"flex-1 min-w-0\">\n                <h4 className=\"font-medium text-sm\">\n                  {notification.title}\n                </h4>\n                <p className=\"text-sm opacity-90 mt-1\">\n                  {notification.message}\n                </p>\n              </div>\n              <button\n                onClick={() => removeNotification(notification.id)}\n                className=\"ml-3 flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </div>\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;;;AAXA;;;;AAaA,MAAM,UAAU;IACd,SAAS,gOAAA,CAAA,kBAAe;IACxB,SAAS,gPAAA,CAAA,0BAAuB;IAChC,MAAM,4OAAA,CAAA,wBAAqB;IAC3B,OAAO,wNAAA,CAAA,cAAW;AACpB;AAEA,MAAM,WAAW;IACf,SAAS;IACT,SAAS;IACT,MAAM;IACN,OAAO;AACT;AAEe,SAAS;;IACtB,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAExD,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC;YAClB,MAAM,OAAO,OAAO,CAAC,aAAa,IAAI,CAAC;YAEvC,qBACE,6LAAC;gBAEC,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,+DACA,QAAQ,CAAC,aAAa,IAAI,CAAC;0BAG7B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;;;;;sCAChB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,aAAa,KAAK;;;;;;8CAErB,6LAAC;oCAAE,WAAU;8CACV,aAAa,OAAO;;;;;;;;;;;;sCAGzB,6LAAC;4BACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;4BACjD,WAAU;sCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;eApBpB,aAAa,EAAE;;;;;QAyB1B;;;;;;AAGN;GA1CwB;;QACwB,4HAAA,CAAA,cAAW;;;KADnC", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Navigation from './Navigation';\nimport NotificationToast from './NotificationToast';\nimport { useAuthStore } from '@/stores/authStore';\n\ninterface AppLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function AppLayout({ children }: AppLayoutProps) {\n  const { user } = useAuthStore();\n\n  if (!user) {\n    return null; // Dit wordt afgehandeld door de auth middleware\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      {/* Main content */}\n      <div className=\"lg:ml-64\">\n        <main className=\"p-4 lg:p-8 pb-20 lg:pb-8\">\n          {children}\n        </main>\n      </div>\n\n      {/* Notification toasts */}\n      <NotificationToast />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAWe,SAAS,UAAU,KAA4B;QAA5B,EAAE,QAAQ,EAAkB,GAA5B;;IAChC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAE5B,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,gDAAgD;IAC/D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;0BAKL,6LAAC,oJAAA,CAAA,UAAiB;;;;;;;;;;;AAGxB;GAtBwB;;QACL,6HAAA,CAAA,eAAY;;;KADP", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base',\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAiG;QAAhG,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO;IAC7F,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, label, error, helperText, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substring(2)}`;\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <input\n          ref={ref}\n          id={inputId}\n          className={cn(\n            'block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm transition-colors',\n            'focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500',\n            'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',\n            error && 'border-red-300 focus:border-red-500 focus:ring-red-500',\n            className\n          )}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,QAAwD;QAAvD,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,OAAO;IACpD,MAAM,UAAU,MAAM,AAAC,SAAgD,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;IAEpE,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,qHACA,6EACA,0EACA,SAAS,0DACT;gBAED,GAAG,KAAK;;;;;;YAEV,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YAE3C,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-lg border border-gray-200 bg-white shadow-sm',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-6 py-4 border-b border-gray-200', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-6 py-4', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-6 py-4 border-t border-gray-200', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nCard.displayName = 'Card';\nCardHeader.displayName = 'CardHeader';\nCardContent.displayName = 'CardContent';\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAkBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;;;AAKP,KAAK,WAAW,GAAG;AACnB,WAAW,WAAW,GAAG;AACzB,YAAY,WAAW,GAAG;AAC1B,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {\n  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';\n  size?: 'sm' | 'md';\n  children: React.ReactNode;\n}\n\nconst Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(\n  ({ className, variant = 'default', size = 'md', children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center font-medium rounded-full';\n    \n    const variants = {\n      default: 'bg-gray-100 text-gray-800',\n      success: 'bg-green-100 text-green-800',\n      warning: 'bg-yellow-100 text-yellow-800',\n      danger: 'bg-red-100 text-red-800',\n      info: 'bg-blue-100 text-blue-800',\n    };\n\n    const sizes = {\n      sm: 'px-2 py-0.5 text-xs',\n      md: 'px-2.5 py-1 text-sm',\n    };\n\n    return (\n      <span\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </span>\n    );\n  }\n);\n\nBadge.displayName = 'Badge';\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,QAAsE;QAArE,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;IAClE,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/index.ts"], "sourcesContent": ["export { default as But<PERSON> } from './Button';\nexport { default as Input } from './Input';\nexport { Card, CardHeader, CardContent, CardFooter } from './Card';\nexport { default as Badge } from './Badge';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/reports/ReportsList.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  MagnifyingGlassIcon,\n  PlusIcon,\n  DocumentTextIcon,\n  FunnelIcon,\n  CalendarDaysIcon,\n  UserIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  XCircleIcon\n} from '@heroicons/react/24/outline';\nimport { Card, CardHeader, CardContent, Button, Input, Badge } from '@/components/ui';\nimport { Report, Client, CareGoal } from '@/types';\nimport { formatDate, getStatusColor, getStatusText } from '@/utils';\n\ninterface EnrichedReport extends Report {\n  clientCode: string;\n  careGoalTitle?: string;\n}\n\ninterface ReportsListProps {\n  onAddReport?: () => void;\n}\n\nexport default function ReportsList({ onAddReport }: ReportsListProps) {\n  const [reports, setReports] = useState<EnrichedReport[]>([]);\n  const [clients, setClients] = useState<Client[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showFilters, setShowFilters] = useState(false);\n  \n  // Filter states\n  const [filters, setFilters] = useState({\n    clientId: '',\n    status: '',\n    dateFrom: '',\n    dateTo: '',\n  });\n\n  useEffect(() => {\n    fetchReports();\n    fetchClients();\n  }, [filters]);\n\n  const fetchReports = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      \n      Object.entries(filters).forEach(([key, value]) => {\n        if (value) params.append(key, value);\n      });\n\n      const response = await fetch(`/api/reports?${params}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setReports(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching reports:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchClients = async () => {\n    try {\n      const response = await fetch('/api/clients?active=true');\n      const data = await response.json();\n      if (data.success) {\n        setClients(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching clients:', error);\n    }\n  };\n\n  const handleFilterChange = (key: string, value: string) => {\n    setFilters(prev => ({ ...prev, [key]: value }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      clientId: '',\n      status: '',\n      dateFrom: '',\n      dateTo: '',\n    });\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'complete':\n        return <CheckCircleIcon className=\"h-5 w-5 text-green-600\" />;\n      case 'partial':\n        return <ExclamationTriangleIcon className=\"h-5 w-5 text-yellow-600\" />;\n      case 'missing':\n        return <XCircleIcon className=\"h-5 w-5 text-red-600\" />;\n      default:\n        return <DocumentTextIcon className=\"h-5 w-5 text-gray-600\" />;\n    }\n  };\n\n  const getStatusStats = () => {\n    const stats = {\n      complete: reports.filter(r => r.status === 'complete').length,\n      partial: reports.filter(r => r.status === 'partial').length,\n      missing: reports.filter(r => r.status === 'missing').length,\n    };\n    return stats;\n  };\n\n  const stats = getStatusStats();\n\n  if (loading) {\n    return (\n      <Card>\n        <CardContent className=\"p-8 text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-2 text-gray-500\">Rapportages laden...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Rapportages</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Overzicht van alle rapportages en hun status\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setShowFilters(!showFilters)}\n          >\n            <FunnelIcon className=\"h-4 w-4 mr-2\" />\n            Filters\n          </Button>\n          <Button onClick={onAddReport}>\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Nieuwe Rapportage\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-green-100 rounded-lg\">\n                <CheckCircleIcon className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Compleet</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.complete}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-yellow-100 rounded-lg\">\n                <ExclamationTriangleIcon className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Gedeeltelijk</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.partial}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-red-100 rounded-lg\">\n                <XCircleIcon className=\"h-6 w-6 text-red-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Ontbreekt</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.missing}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Filters */}\n      {showFilters && (\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Filters</h3>\n              <Button variant=\"ghost\" size=\"sm\" onClick={clearFilters}>\n                Wissen\n              </Button>\n            </div>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Cliënt\n                </label>\n                <select\n                  value={filters.clientId}\n                  onChange={(e) => handleFilterChange('clientId', e.target.value)}\n                  className=\"block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                >\n                  <option value=\"\">Alle cliënten</option>\n                  {clients.map((client) => (\n                    <option key={client.id} value={client.id}>\n                      {client.clientCode}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Status\n                </label>\n                <select\n                  value={filters.status}\n                  onChange={(e) => handleFilterChange('status', e.target.value)}\n                  className=\"block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                >\n                  <option value=\"\">Alle statussen</option>\n                  <option value=\"complete\">Compleet</option>\n                  <option value=\"partial\">Gedeeltelijk</option>\n                  <option value=\"missing\">Ontbreekt</option>\n                </select>\n              </div>\n\n              <Input\n                type=\"date\"\n                label=\"Van datum\"\n                value={filters.dateFrom}\n                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}\n              />\n\n              <Input\n                type=\"date\"\n                label=\"Tot datum\"\n                value={filters.dateTo}\n                onChange={(e) => handleFilterChange('dateTo', e.target.value)}\n              />\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Reports List */}\n      <div className=\"space-y-4\">\n        {reports.map((report) => (\n          <Card key={report.id} className=\"hover:shadow-md transition-shadow\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-start\">\n                  {getStatusIcon(report.status)}\n                  <div className=\"ml-3 flex-1\">\n                    <h3 className=\"font-semibold text-gray-900\">{report.title}</h3>\n                    \n                    <div className=\"flex items-center mt-1 space-x-4 text-sm text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <UserIcon className=\"h-4 w-4 mr-1\" />\n                        {report.clientCode}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <CalendarDaysIcon className=\"h-4 w-4 mr-1\" />\n                        {formatDate(report.reportDate)}\n                      </div>\n                    </div>\n\n                    {report.careGoalTitle && (\n                      <p className=\"text-sm text-blue-600 mt-1\">\n                        Zorgdoel: {report.careGoalTitle}\n                      </p>\n                    )}\n\n                    {report.content && (\n                      <p className=\"text-gray-600 mt-2 line-clamp-2\">\n                        {report.content}\n                      </p>\n                    )}\n\n                    <p className=\"text-xs text-gray-400 mt-2\">\n                      Aangemaakt: {formatDate(report.createdAt)}\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex flex-col items-end space-y-2\">\n                  <Badge \n                    variant={\n                      report.status === 'complete' ? 'success' : \n                      report.status === 'partial' ? 'warning' : 'danger'\n                    }\n                  >\n                    {getStatusText(report.status)}\n                  </Badge>\n                  \n                  <Button variant=\"outline\" size=\"sm\">\n                    Bekijken\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {reports.length === 0 && (\n        <Card>\n          <CardContent className=\"p-8 text-center\">\n            <DocumentTextIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Geen rapportages gevonden\n            </h3>\n            <p className=\"text-gray-500 mb-4\">\n              Er zijn geen rapportages die voldoen aan uw criteria.\n            </p>\n            <Button onClick={onAddReport}>\n              <PlusIcon className=\"h-4 w-4 mr-2\" />\n              Eerste Rapportage Toevoegen\n            </Button>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAhBA;;;;;AA2Be,SAAS,YAAY,KAAiC;QAAjC,EAAE,WAAW,EAAoB,GAAjC;;IAClC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,gBAAgB;IAChB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;IACV;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;YACA;QACF;gCAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI;YAEnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC;oBAAC,CAAC,KAAK,MAAM;gBAC3C,IAAI,OAAO,OAAO,MAAM,CAAC,KAAK;YAChC;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAsB,OAAP;YAC7C,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAa;QACvC,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IAC/C;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,UAAU;YACV,QAAQ;YACR,UAAU;YACV,QAAQ;QACV;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,gOAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,gPAAA,CAAA,0BAAuB;oBAAC,WAAU;;;;;;YAC5C,KAAK;gBACH,qBAAO,6LAAC,wNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,kOAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;QACvC;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,QAAQ;YACZ,UAAU,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;YAC7D,SAAS,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;YAC3D,SAAS,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC7D;QACA,OAAO;IACT;IAEA,MAAM,QAAQ;IAEd,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,0KAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,eAAe,CAAC;;kDAE/B,6LAAC,sNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGzC,6LAAC,0KAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;kDAE7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvE,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gPAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;;;;;;kDAErC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMtE,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,wNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQvE,6BACC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC,0KAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CAAc;;;;;;;;;;;;;;;;;kCAK7D,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC9D,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wDAAuB,OAAO,OAAO,EAAE;kEACrC,OAAO,UAAU;uDADP,OAAO,EAAE;;;;;;;;;;;;;;;;;8CAO5B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,QAAQ,MAAM;4CACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;4CAC5D,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;8CAI5B,6LAAC,wKAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,OAAM;oCACN,OAAO,QAAQ,QAAQ;oCACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;8CAGhE,6LAAC,wKAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,OAAM;oCACN,OAAO,QAAQ,MAAM;oCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;0BAQtE,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,mIAAA,CAAA,OAAI;wBAAiB,WAAU;kCAC9B,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,cAAc,OAAO,MAAM;0DAC5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+B,OAAO,KAAK;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,kNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,OAAO,UAAU;;;;;;;0EAEpB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,kOAAA,CAAA,mBAAgB;wEAAC,WAAU;;;;;;oEAC3B,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,UAAU;;;;;;;;;;;;;oDAIhC,OAAO,aAAa,kBACnB,6LAAC;wDAAE,WAAU;;4DAA6B;4DAC7B,OAAO,aAAa;;;;;;;oDAIlC,OAAO,OAAO,kBACb,6LAAC;wDAAE,WAAU;kEACV,OAAO,OAAO;;;;;;kEAInB,6LAAC;wDAAE,WAAU;;4DAA6B;4DAC3B,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;;;;;;;;;;;;;;;;;;;kDAK9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,wKAAA,CAAA,QAAK;gDACJ,SACE,OAAO,MAAM,KAAK,aAAa,YAC/B,OAAO,MAAM,KAAK,YAAY,YAAY;0DAG3C,CAAA,GAAA,wHAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,MAAM;;;;;;0DAG9B,6LAAC,0KAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;0DAAK;;;;;;;;;;;;;;;;;;;;;;;uBA/CjC,OAAO,EAAE;;;;;;;;;;YAyDvB,QAAQ,MAAM,KAAK,mBAClB,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,kOAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;sCAC5B,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC,0KAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,6LAAC,kNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GA3TwB;KAAA", "debugId": null}}, {"offset": {"line": 2053, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/reports/AddReportModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { Button, Input } from '@/components/ui';\nimport { useAppStore } from '@/stores/appStore';\nimport { Client, CareGoal } from '@/types';\n\nconst reportSchema = z.object({\n  clientId: z.string().min(1, 'Selecteer een cliënt'),\n  careGoalId: z.string().optional(),\n  title: z.string().min(1, 'Titel is verplicht'),\n  content: z.string().optional(),\n  reportDate: z.string().min(1, 'Rapportage datum is verplicht'),\n});\n\ntype ReportFormData = z.infer<typeof reportSchema>;\n\ninterface AddReportModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: () => void;\n}\n\nexport default function AddReportModal({ isOpen, onClose, onSuccess }: AddReportModalProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [clients, setClients] = useState<Client[]>([]);\n  const [careGoals, setCareGoals] = useState<CareGoal[]>([]);\n  const [selectedClientId, setSelectedClientId] = useState('');\n  const { addNotification } = useAppStore();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n    watch,\n    setValue,\n  } = useForm<ReportFormData>({\n    resolver: zodResolver(reportSchema),\n    defaultValues: {\n      reportDate: new Date().toISOString().split('T')[0],\n    },\n  });\n\n  const watchedClientId = watch('clientId');\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchClients();\n    }\n  }, [isOpen]);\n\n  useEffect(() => {\n    if (watchedClientId) {\n      setSelectedClientId(watchedClientId);\n      fetchCareGoals(watchedClientId);\n      setValue('careGoalId', ''); // Reset care goal when client changes\n    }\n  }, [watchedClientId, setValue]);\n\n  const fetchClients = async () => {\n    try {\n      const response = await fetch('/api/clients?active=true');\n      const data = await response.json();\n      if (data.success) {\n        setClients(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching clients:', error);\n    }\n  };\n\n  const fetchCareGoals = async (clientId: string) => {\n    try {\n      const response = await fetch(`/api/clients/${clientId}`);\n      const data = await response.json();\n      if (data.success) {\n        const activeGoals = data.data.careGoals.filter((goal: CareGoal) => goal.status === 'active');\n        setCareGoals(activeGoals);\n      }\n    } catch (error) {\n      console.error('Error fetching care goals:', error);\n      setCareGoals([]);\n    }\n  };\n\n  const onSubmit = async (data: ReportFormData) => {\n    try {\n      setIsSubmitting(true);\n\n      const response = await fetch('/api/reports', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          clientId: data.clientId,\n          careGoalId: data.careGoalId || undefined,\n          title: data.title,\n          content: data.content,\n          reportDate: data.reportDate,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.message || 'Er is een fout opgetreden');\n      }\n\n      addNotification({\n        type: 'success',\n        title: 'Rapportage toegevoegd',\n        message: `Rapportage \"${data.title}\" is succesvol toegevoegd`,\n      });\n\n      reset();\n      onSuccess();\n      onClose();\n\n    } catch (error) {\n      const message = error instanceof Error ? error.message : 'Er is een fout opgetreden';\n      addNotification({\n        type: 'error',\n        title: 'Fout bij toevoegen',\n        message,\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    reset();\n    setSelectedClientId('');\n    setCareGoals([]);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">\n            Nieuwe Rapportage Toevoegen\n          </h2>\n          <button\n            onClick={handleClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <XMarkIcon className=\"h-5 w-5\" />\n          </button>\n        </div>\n\n        {/* Content */}\n        <form onSubmit={handleSubmit(onSubmit)} className=\"p-6\">\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Cliënt *\n              </label>\n              <select\n                {...register('clientId')}\n                className=\"block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n              >\n                <option value=\"\">Selecteer een cliënt</option>\n                {clients.map((client) => (\n                  <option key={client.id} value={client.id}>\n                    {client.clientCode}\n                  </option>\n                ))}\n              </select>\n              {errors.clientId && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.clientId.message}</p>\n              )}\n            </div>\n\n            {selectedClientId && careGoals.length > 0 && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Zorgdoel (optioneel)\n                </label>\n                <select\n                  {...register('careGoalId')}\n                  className=\"block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                >\n                  <option value=\"\">Geen specifiek zorgdoel</option>\n                  {careGoals.map((goal) => (\n                    <option key={goal.id} value={goal.id}>\n                      {goal.title}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            )}\n\n            <Input\n              {...register('title')}\n              label=\"Titel *\"\n              placeholder=\"Bijv. Voortgang mobiliteit week 1\"\n              error={errors.title?.message}\n            />\n\n            <Input\n              {...register('reportDate')}\n              type=\"date\"\n              label=\"Rapportage datum *\"\n              error={errors.reportDate?.message}\n            />\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Inhoud\n              </label>\n              <textarea\n                {...register('content')}\n                rows={6}\n                className=\"block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                placeholder=\"Beschrijf de observaties, voortgang en eventuele bijzonderheden...\"\n              />\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Laat leeg voor een gedeeltelijke rapportage die later kan worden aangevuld.\n              </p>\n            </div>\n\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-blue-900 mb-2\">\n                Privacy & Beveiliging\n              </h4>\n              <p className=\"text-sm text-blue-700\">\n                Zorg ervoor dat geen persoonlijke identificeerbare informatie (PII) \n                wordt opgenomen in de rapportage. Gebruik alleen cliëntcodes en \n                professionele observaties.\n              </p>\n            </div>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-3 mt-6\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n              disabled={isSubmitting}\n            >\n              Annuleren\n            </Button>\n            <Button\n              type=\"submit\"\n              loading={isSubmitting}\n              disabled={isSubmitting}\n            >\n              Rapportage Toevoegen\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AARA;;;;;;;;AAWA,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAChC;AAUe,SAAS,eAAe,KAAmD;QAAnD,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAuB,GAAnD;QAoLlB,eAOA;;IA1LnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEtC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,KAAK,EACL,QAAQ,EACT,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkB;QAC1B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD;IACF;IAEA,MAAM,kBAAkB,MAAM;IAE9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;mCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,iBAAiB;gBACnB,oBAAoB;gBACpB,eAAe;gBACf,SAAS,cAAc,KAAK,sCAAsC;YACpE;QACF;mCAAG;QAAC;QAAiB;KAAS;IAE9B,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAwB,OAAT;YAC7C,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,cAAc,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAmB,KAAK,MAAM,KAAK;gBACnF,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,aAAa,EAAE;QACjB;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,gBAAgB;YAEhB,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU,IAAI;oBAC/B,OAAO,KAAK,KAAK;oBACjB,SAAS,KAAK,OAAO;oBACrB,YAAY,KAAK,UAAU;gBAC7B;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS,AAAC,eAAyB,OAAX,KAAK,KAAK,EAAC;YACrC;YAEA;YACA;YACA;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP;YACF;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB;QACA,oBAAoB;QACpB,aAAa,EAAE;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKzB,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAChD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACE,GAAG,SAAS,WAAW;4CACxB,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wDAAuB,OAAO,OAAO,EAAE;kEACrC,OAAO,UAAU;uDADP,OAAO,EAAE;;;;;;;;;;;wCAKzB,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;gCAIpE,oBAAoB,UAAU,MAAM,GAAG,mBACtC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACE,GAAG,SAAS,aAAa;4CAC1B,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;wDAAqB,OAAO,KAAK,EAAE;kEACjC,KAAK,KAAK;uDADA,KAAK,EAAE;;;;;;;;;;;;;;;;;8CAQ5B,6LAAC,wKAAA,CAAA,QAAK;oCACH,GAAG,SAAS,QAAQ;oCACrB,OAAM;oCACN,aAAY;oCACZ,KAAK,GAAE,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,OAAO;;;;;;8CAG9B,6LAAC,wKAAA,CAAA,QAAK;oCACH,GAAG,SAAS,aAAa;oCAC1B,MAAK;oCACL,OAAM;oCACN,KAAK,GAAE,qBAAA,OAAO,UAAU,cAAjB,yCAAA,mBAAmB,OAAO;;;;;;8CAGnC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACE,GAAG,SAAS,UAAU;4CACvB,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;sDAEd,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAG/C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCASzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0KAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,6LAAC,0KAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,UAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA/OwB;;QAKM,4HAAA,CAAA,cAAW;QASnC,iKAAA,CAAA,UAAO;;;KAdW", "debugId": null}}, {"offset": {"line": 2477, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/app/rapportages/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { useAuthStore } from \"@/stores/authStore\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport ReportsList from \"@/components/reports/ReportsList\";\nimport AddReportModal from \"@/components/reports/AddReportModal\";\n\nexport default function RapportagesPage() {\n  const router = useRouter();\n  const { user } = useAuthStore();\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [refreshKey, setRefreshKey] = useState(0);\n\n  useEffect(() => {\n    if (!user) {\n      router.push(\"/\");\n    }\n  }, [user, router]);\n\n  const handleAddReport = () => {\n    setShowAddModal(true);\n  };\n\n  const handleAddSuccess = () => {\n    setRefreshKey((prev) => prev + 1);\n  };\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <AppLayout>\n      <ReportsList key={refreshKey} onAddReport={handleAddReport} />\n\n      <AddReportModal\n        isOpen={showAddModal}\n        onClose={() => setShowAddModal(false)}\n        onSuccess={handleAddSuccess}\n      />\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;YACd;QACF;oCAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,kBAAkB;QACtB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,cAAc,CAAC,OAAS,OAAO;IACjC;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC,4IAAA,CAAA,UAAS;;0BACR,6LAAC,+IAAA,CAAA,UAAW;gBAAkB,aAAa;eAAzB;;;;;0BAElB,6LAAC,kJAAA,CAAA,UAAc;gBACb,QAAQ;gBACR,SAAS,IAAM,gBAAgB;gBAC/B,WAAW;;;;;;;;;;;;AAInB;GAnCwB;;QACP,qIAAA,CAAA,YAAS;QACP,6HAAA,CAAA,eAAY;;;KAFP", "debugId": null}}]}