{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/utils/index.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\n\n// Utility voor het combineren van CSS classes\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n// Date formatting utilities\nexport function formatDate(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString(locale, {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function formatDateTime(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleString(locale, {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\nexport function formatTime(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleTimeString(locale, {\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Status utilities\nexport function getStatusColor(status: string): string {\n  switch (status) {\n    case 'complete':\n    case 'completed':\n    case 'active':\n      return 'text-green-600 bg-green-100';\n    case 'partial':\n    case 'paused':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'missing':\n    case 'cancelled':\n      return 'text-red-600 bg-red-100';\n    case 'scheduled':\n      return 'text-blue-600 bg-blue-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getStatusText(status: string): string {\n  switch (status) {\n    case 'complete':\n      return 'Compleet';\n    case 'partial':\n      return 'Gedeeltelijk';\n    case 'missing':\n      return 'Ontbreekt';\n    case 'completed':\n      return 'Voltooid';\n    case 'active':\n      return 'Actief';\n    case 'paused':\n      return 'Gepauzeerd';\n    case 'scheduled':\n      return 'Gepland';\n    case 'cancelled':\n      return 'Geannuleerd';\n    default:\n      return status;\n  }\n}\n\nexport function getPriorityColor(priority: string): string {\n  switch (priority) {\n    case 'high':\n      return 'text-red-600 bg-red-100';\n    case 'medium':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'low':\n      return 'text-green-600 bg-green-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getPriorityText(priority: string): string {\n  switch (priority) {\n    case 'high':\n      return 'Hoog';\n    case 'medium':\n      return 'Gemiddeld';\n    case 'low':\n      return 'Laag';\n    default:\n      return priority;\n  }\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidFQDN(email: string): boolean {\n  // Controleer of email een FQDN format heeft (<EMAIL>)\n  const fqdnRegex = /^[^\\s@]+@[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\\.[a-zA-Z]{2,}$/;\n  return fqdnRegex.test(email);\n}\n\n// Text utilities\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function capitalizeFirst(text: string): string {\n  return text.charAt(0).toUpperCase() + text.slice(1);\n}\n\n// Array utilities\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key];\n    const bVal = b[key];\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n    return 0;\n  });\n}\n\n// Local storage utilities\nexport function getFromStorage(key: string): string | null {\n  if (typeof window === 'undefined') return null;\n  try {\n    return localStorage.getItem(key);\n  } catch {\n    return null;\n  }\n}\n\nexport function setToStorage(key: string, value: string): void {\n  if (typeof window === 'undefined') return;\n  try {\n    localStorage.setItem(key, value);\n  } catch {\n    // Silently fail\n  }\n}\n\nexport function removeFromStorage(key: string): void {\n  if (typeof window === 'undefined') return;\n  try {\n    localStorage.removeItem(key);\n  } catch {\n    // Silently fail\n  }\n}\n\n// API utilities\nexport function buildQueryString(params: Record<string, any>): string {\n  const searchParams = new URLSearchParams();\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== '') {\n      if (value instanceof Date) {\n        searchParams.append(key, value.toISOString());\n      } else {\n        searchParams.append(key, String(value));\n      }\n    }\n  });\n  \n  return searchParams.toString();\n}\n\n// Error handling utilities\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'Er is een onbekende fout opgetreden';\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Generate unique ID\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAGO,SAAS,WAAW,IAAmB;QAAE,SAAA,iEAAiB;IAC/D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,QAAQ;QACxC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;QAAE,SAAA,iEAAiB;IACnE,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,cAAc,CAAC,QAAQ;QACpC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,IAAmB;QAAE,SAAA,iEAAiB;IAC/D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,QAAQ;QACxC,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,KAAa;IACvC,mEAAmE;IACnE,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;AACnD;AAGO,SAAS,QAAW,KAAU,EAAE,GAAY;IACjD,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY;QAAE,YAAA,iEAA4B;IAC9E,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,eAAe,GAAW;IACxC;;IACA,IAAI;QACF,OAAO,aAAa,OAAO,CAAC;IAC9B,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,aAAa,GAAW,EAAE,KAAa;IACrD;;IACA,IAAI;QACF,aAAa,OAAO,CAAC,KAAK;IAC5B,EAAE,UAAM;IACN,gBAAgB;IAClB;AACF;AAEO,SAAS,kBAAkB,GAAW;IAC3C;;IACA,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,UAAM;IACN,gBAAgB;IAClB;AACF;AAGO,SAAS,iBAAiB,MAA2B;IAC1D,MAAM,eAAe,IAAI;IAEzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC;YAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,iBAAiB,MAAM;gBACzB,aAAa,MAAM,CAAC,KAAK,MAAM,WAAW;YAC5C,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;IACF;IAEA,OAAO,aAAa,QAAQ;AAC9B;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/stores/authStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User, AuthState } from '@/types';\nimport { getFromStorage, setToStorage, removeFromStorage } from '@/utils';\n\ninterface AuthStore extends AuthState {\n  login: (user: User, token: string) => void;\n  logout: () => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n  updateUser: (user: Partial<User>) => void;\n}\n\nexport const useAuthStore = create<AuthStore>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      token: null,\n      isLoading: false,\n      error: null,\n\n      login: (user: User, token: string) => {\n        set({ user, token, error: null });\n        setToStorage('auth_token', token);\n      },\n\n      logout: () => {\n        set({ user: null, token: null, error: null });\n        removeFromStorage('auth_token');\n      },\n\n      setLoading: (isLoading: boolean) => {\n        set({ isLoading });\n      },\n\n      setError: (error: string | null) => {\n        set({ error });\n      },\n\n      clearError: () => {\n        set({ error: null });\n      },\n\n      updateUser: (userData: Partial<User>) => {\n        const currentUser = get().user;\n        if (currentUser) {\n          set({ user: { ...currentUser, ...userData } });\n        }\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAWO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,OAAO;QACP,WAAW;QACX,OAAO;QAEP,OAAO,CAAC,MAAY;YAClB,IAAI;gBAAE;gBAAM;gBAAO,OAAO;YAAK;YAC/B,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,cAAc;QAC7B;QAEA,QAAQ;YACN,IAAI;gBAAE,MAAM;gBAAM,OAAO;gBAAM,OAAO;YAAK;YAC3C,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EAAE;QACpB;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;YAAU;QAClB;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,YAAY,CAAC;YACX,MAAM,cAAc,MAAM,IAAI;YAC9B,IAAI,aAAa;gBACf,IAAI;oBAAE,MAAM;wBAAE,GAAG,WAAW;wBAAE,GAAG,QAAQ;oBAAC;gBAAE;YAC9C;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;QACpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/stores/appStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { AppState, Tenant, Notification } from '@/types';\nimport { generateId } from '@/utils';\n\ninterface AppStore extends AppState {\n  setSidebarOpen: (open: boolean) => void;\n  toggleSidebar: () => void;\n  setCurrentTenant: (tenant: Tenant | null) => void;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationAsRead: (id: string) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n}\n\nexport const useAppStore = create<AppStore>((set, get) => ({\n  sidebarOpen: false,\n  currentTenant: null,\n  notifications: [],\n\n  setSidebarOpen: (sidebarOpen: boolean) => {\n    set({ sidebarOpen });\n  },\n\n  toggleSidebar: () => {\n    set((state) => ({ sidebarOpen: !state.sidebarOpen }));\n  },\n\n  setCurrentTenant: (currentTenant: Tenant | null) => {\n    set({ currentTenant });\n  },\n\n  addNotification: (notificationData) => {\n    const notification: Notification = {\n      ...notificationData,\n      id: generateId(),\n      timestamp: new Date(),\n      read: false,\n    };\n\n    set((state) => ({\n      notifications: [notification, ...state.notifications],\n    }));\n\n    // Auto-remove notification after 5 seconds for non-error types\n    if (notificationData.type !== 'error') {\n      setTimeout(() => {\n        get().removeNotification(notification.id);\n      }, 5000);\n    }\n  },\n\n  markNotificationAsRead: (id: string) => {\n    set((state) => ({\n      notifications: state.notifications.map((notification) =>\n        notification.id === id ? { ...notification, read: true } : notification\n      ),\n    }));\n  },\n\n  removeNotification: (id: string) => {\n    set((state) => ({\n      notifications: state.notifications.filter((notification) => notification.id !== id),\n    }));\n  },\n\n  clearNotifications: () => {\n    set({ notifications: [] });\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAYO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAY,CAAC,KAAK,MAAQ,CAAC;QACzD,aAAa;QACb,eAAe;QACf,eAAe,EAAE;QAEjB,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;QACpB;QAEA,eAAe;YACb,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QACrD;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE;YAAc;QACtB;QAEA,iBAAiB,CAAC;YAChB,MAAM,eAA6B;gBACjC,GAAG,gBAAgB;gBACnB,IAAI,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD;gBACb,WAAW,IAAI;gBACf,MAAM;YACR;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBAAC;2BAAiB,MAAM,aAAa;qBAAC;gBACvD,CAAC;YAED,+DAA+D;YAC/D,IAAI,iBAAiB,IAAI,KAAK,SAAS;gBACrC,WAAW;oBACT,MAAM,kBAAkB,CAAC,aAAa,EAAE;gBAC1C,GAAG;YACL;QACF;QAEA,wBAAwB,CAAC;YACvB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eACtC,aAAa,EAAE,KAAK,KAAK;4BAAE,GAAG,YAAY;4BAAE,MAAM;wBAAK,IAAI;gBAE/D,CAAC;QACH;QAEA,oBAAoB,CAAC;YACnB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,eAAiB,aAAa,EAAE,KAAK;gBAClF,CAAC;QACH;QAEA,oBAAoB;YAClB,IAAI;gBAAE,eAAe,EAAE;YAAC;QAC1B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  CalendarDaysIcon,\n  UserGroupIcon,\n  DocumentTextIcon,\n  CreditCardIcon,\n  CogIcon,\n  HomeIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { cn } from '@/utils';\nimport { useAuthStore } from '@/stores/authStore';\nimport { useAppStore } from '@/stores/appStore';\nimport { NavItem } from '@/types';\n\nconst navigationItems: NavItem[] = [\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: HomeIcon,\n  },\n  {\n    name: 'Agenda',\n    href: '/agenda',\n    icon: CalendarDaysIcon,\n  },\n  {\n    name: '<PERSON><PERSON><PERSON>nten',\n    href: '/clienten',\n    icon: UserGroupIcon,\n  },\n  {\n    name: 'Rapportages',\n    href: '/rapportages',\n    icon: DocumentTextIcon,\n  },\n  {\n    name: 'Facturatie',\n    href: '/facturatie',\n    icon: CreditCardIcon,\n    adminOnly: true,\n  },\n  {\n    name: 'Beheer',\n    href: '/beheer',\n    icon: CogIcon,\n    adminOnly: true,\n  },\n];\n\ninterface NavigationProps {\n  className?: string;\n}\n\nexport default function Navigation({ className }: NavigationProps) {\n  const pathname = usePathname();\n  const { user } = useAuthStore();\n  const { sidebarOpen, setSidebarOpen } = useAppStore();\n\n  const filteredItems = navigationItems.filter(item => \n    !item.adminOnly || user?.role === 'admin'\n  );\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <button\n          onClick={() => setSidebarOpen(!sidebarOpen)}\n          className=\"p-2 rounded-lg bg-white shadow-md border border-gray-200\"\n        >\n          {sidebarOpen ? (\n            <XMarkIcon className=\"h-6 w-6 text-gray-600\" />\n          ) : (\n            <Bars3Icon className=\"h-6 w-6 text-gray-600\" />\n          )}\n        </button>\n      </div>\n\n      {/* Mobile overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <nav className={cn(\n        'fixed top-0 left-0 h-full bg-white border-r border-gray-200 shadow-sm z-40 transition-transform duration-300',\n        'lg:translate-x-0 lg:static lg:z-auto',\n        sidebarOpen ? 'translate-x-0' : '-translate-x-full',\n        'w-64',\n        className\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"p-6 border-b border-gray-200\">\n            <h1 className=\"text-xl font-bold text-gray-900\">\n              ZorgPortaal Plus\n            </h1>\n            {user && (\n              <p className=\"text-sm text-gray-500 mt-1\">\n                {user.name}\n              </p>\n            )}\n          </div>\n\n          {/* Navigation items */}\n          <div className=\"flex-1 px-4 py-6 space-y-2\">\n            {filteredItems.map((item) => {\n              const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n              \n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setSidebarOpen(false)}\n                  className={cn(\n                    'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                  )}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* User info and logout */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"text-xs text-gray-500 mb-2\">\n              Tenant: {user?.tenantId}\n            </div>\n            <button\n              onClick={() => {\n                useAuthStore.getState().logout();\n                setSidebarOpen(false);\n              }}\n              className=\"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              Uitloggen\n            </button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Bottom navigation for mobile */}\n      <div className=\"lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-30\">\n        <div className=\"flex\">\n          {filteredItems.slice(0, 4).map((item) => {\n            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n            \n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  'flex-1 flex flex-col items-center py-2 px-1 text-xs',\n                  isActive\n                    ? 'text-blue-700'\n                    : 'text-gray-500'\n                )}\n              >\n                <item.icon className=\"h-5 w-5 mb-1\" />\n                <span className=\"truncate\">{item.name}</span>\n              </Link>\n            );\n          })}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;AAoBA,MAAM,kBAA6B;IACjC;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kOAAA,CAAA,mBAAgB;IACxB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,4NAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kOAAA,CAAA,mBAAgB;IACxB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,8NAAA,CAAA,iBAAc;QACpB,WAAW;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gNAAA,CAAA,UAAO;QACb,WAAW;IACb;CACD;AAMc,SAAS,WAAW,KAA8B;QAA9B,EAAE,SAAS,EAAmB,GAA9B;;IACjC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAElD,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,OAC3C,CAAC,KAAK,SAAS,IAAI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAGpC,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS,IAAM,eAAe,CAAC;oBAC/B,WAAU;8BAET,4BACC,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;6CAErB,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAM1B,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACf,gHACA,wCACA,cAAc,kBAAkB,qBAChC,QACA;0BAEA,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;gCAG/C,sBACC,6LAAC;oCAAE,WAAU;8CACV,KAAK,IAAI;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC;gCAClB,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;gCAE3E,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,eAAe;oCAC9B,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,wDACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCAXL,KAAK,IAAI;;;;;4BAcpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAA6B;wCACjC,iBAAA,2BAAA,KAAM,QAAQ;;;;;;;8CAEzB,6LAAC;oCACC,SAAS;wCACP,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;wCAC9B,eAAe;oCACjB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wBAC9B,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;wBAE3E,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,uDACA,WACI,kBACA;;8CAGN,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAK,WAAU;8CAAY,KAAK,IAAI;;;;;;;2BAVhC,KAAK,IAAI;;;;;oBAapB;;;;;;;;;;;;;AAKV;GA1HwB;;QACL,qIAAA,CAAA,cAAW;QACX,6HAAA,CAAA,eAAY;QACW,4HAAA,CAAA,cAAW;;;KAH7B", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/layout/NotificationToast.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { \n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  InformationCircleIcon,\n  XCircleIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { useAppStore } from '@/stores/appStore';\nimport { cn } from '@/utils';\n\nconst iconMap = {\n  success: CheckCircleIcon,\n  warning: ExclamationTriangleIcon,\n  info: InformationCircleIcon,\n  error: XCircleIcon,\n};\n\nconst colorMap = {\n  success: 'bg-green-50 border-green-200 text-green-800',\n  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n  info: 'bg-blue-50 border-blue-200 text-blue-800',\n  error: 'bg-red-50 border-red-200 text-red-800',\n};\n\nexport default function NotificationToast() {\n  const { notifications, removeNotification } = useAppStore();\n\n  if (notifications.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {notifications.map((notification) => {\n        const Icon = iconMap[notification.type];\n        \n        return (\n          <div\n            key={notification.id}\n            className={cn(\n              'p-4 rounded-lg border shadow-lg transition-all duration-300',\n              colorMap[notification.type]\n            )}\n          >\n            <div className=\"flex items-start\">\n              <Icon className=\"h-5 w-5 mt-0.5 mr-3 flex-shrink-0\" />\n              <div className=\"flex-1 min-w-0\">\n                <h4 className=\"font-medium text-sm\">\n                  {notification.title}\n                </h4>\n                <p className=\"text-sm opacity-90 mt-1\">\n                  {notification.message}\n                </p>\n              </div>\n              <button\n                onClick={() => removeNotification(notification.id)}\n                className=\"ml-3 flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </div>\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;;;AAXA;;;;AAaA,MAAM,UAAU;IACd,SAAS,gOAAA,CAAA,kBAAe;IACxB,SAAS,gPAAA,CAAA,0BAAuB;IAChC,MAAM,4OAAA,CAAA,wBAAqB;IAC3B,OAAO,wNAAA,CAAA,cAAW;AACpB;AAEA,MAAM,WAAW;IACf,SAAS;IACT,SAAS;IACT,MAAM;IACN,OAAO;AACT;AAEe,SAAS;;IACtB,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAExD,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC;YAClB,MAAM,OAAO,OAAO,CAAC,aAAa,IAAI,CAAC;YAEvC,qBACE,6LAAC;gBAEC,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,+DACA,QAAQ,CAAC,aAAa,IAAI,CAAC;0BAG7B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;;;;;sCAChB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,aAAa,KAAK;;;;;;8CAErB,6LAAC;oCAAE,WAAU;8CACV,aAAa,OAAO;;;;;;;;;;;;sCAGzB,6LAAC;4BACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;4BACjD,WAAU;sCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;eApBpB,aAAa,EAAE;;;;;QAyB1B;;;;;;AAGN;GA1CwB;;QACwB,4HAAA,CAAA,cAAW;;;KADnC", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Navigation from './Navigation';\nimport NotificationToast from './NotificationToast';\nimport { useAuthStore } from '@/stores/authStore';\n\ninterface AppLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function AppLayout({ children }: AppLayoutProps) {\n  const { user } = useAuthStore();\n\n  if (!user) {\n    return null; // Dit wordt afgehandeld door de auth middleware\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      {/* Main content */}\n      <div className=\"lg:ml-64\">\n        <main className=\"p-4 lg:p-8 pb-20 lg:pb-8\">\n          {children}\n        </main>\n      </div>\n\n      {/* Notification toasts */}\n      <NotificationToast />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAWe,SAAS,UAAU,KAA4B;QAA5B,EAAE,QAAQ,EAAkB,GAA5B;;IAChC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAE5B,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,gDAAgD;IAC/D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;0BAKL,6LAAC,oJAAA,CAAA,UAAiB;;;;;;;;;;;AAGxB;GAtBwB;;QACL,6HAAA,CAAA,eAAY;;;KADP", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base',\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAiG;QAAhG,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO;IAC7F,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, label, error, helperText, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substring(2)}`;\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <input\n          ref={ref}\n          id={inputId}\n          className={cn(\n            'block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm transition-colors',\n            'focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500',\n            'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',\n            error && 'border-red-300 focus:border-red-500 focus:ring-red-500',\n            className\n          )}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,QAAwD;QAAvD,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,OAAO;IACpD,MAAM,UAAU,MAAM,AAAC,SAAgD,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;IAEpE,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,qHACA,6EACA,0EACA,SAAS,0DACT;gBAED,GAAG,KAAK;;;;;;YAEV,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YAE3C,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-lg border border-gray-200 bg-white shadow-sm',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-6 py-4 border-b border-gray-200', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-6 py-4', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-6 py-4 border-t border-gray-200', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nCard.displayName = 'Card';\nCardHeader.displayName = 'CardHeader';\nCardContent.displayName = 'CardContent';\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAkBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;;;AAKP,KAAK,WAAW,GAAG;AACnB,WAAW,WAAW,GAAG;AACzB,YAAY,WAAW,GAAG;AAC1B,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {\n  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';\n  size?: 'sm' | 'md';\n  children: React.ReactNode;\n}\n\nconst Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(\n  ({ className, variant = 'default', size = 'md', children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center font-medium rounded-full';\n    \n    const variants = {\n      default: 'bg-gray-100 text-gray-800',\n      success: 'bg-green-100 text-green-800',\n      warning: 'bg-yellow-100 text-yellow-800',\n      danger: 'bg-red-100 text-red-800',\n      info: 'bg-blue-100 text-blue-800',\n    };\n\n    const sizes = {\n      sm: 'px-2 py-0.5 text-xs',\n      md: 'px-2.5 py-1 text-sm',\n    };\n\n    return (\n      <span\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </span>\n    );\n  }\n);\n\nBadge.displayName = 'Badge';\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,QAAsE;QAArE,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;IAClE,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/index.ts"], "sourcesContent": ["export { default as But<PERSON> } from './Button';\nexport { default as Input } from './Input';\nexport { Card, CardHeader, CardContent, CardFooter } from './Card';\nexport { default as Badge } from './Badge';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/billing/BillingOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  CalendarDaysIcon,\n  UserGroupIcon,\n  DocumentTextIcon,\n  ChartBarIcon,\n  ArrowDownTrayIcon,\n  CurrencyEuroIcon,\n  BuildingOfficeIcon\n} from '@heroicons/react/24/outline';\nimport { Card, CardHeader, CardContent, Button, Input } from '@/components/ui';\nimport { BillingMetrics, Tenant } from '@/types';\nimport { formatDate } from '@/utils';\n\nexport default function BillingOverview() {\n  const [metrics, setMetrics] = useState<BillingMetrics[]>([]);\n  const [tenants, setTenants] = useState<Tenant[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedPeriod, setSelectedPeriod] = useState({\n    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0],\n  });\n\n  useEffect(() => {\n    fetchMetrics();\n    fetchTenants();\n  }, [selectedPeriod]);\n\n  const fetchMetrics = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        startDate: selectedPeriod.start,\n        endDate: selectedPeriod.end,\n      });\n\n      const response = await fetch(`/api/billing/metrics?${params}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setMetrics(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching billing metrics:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchTenants = async () => {\n    try {\n      const response = await fetch('/api/admin/tenants?active=true');\n      const data = await response.json();\n      if (data.success) {\n        setTenants(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching tenants:', error);\n    }\n  };\n\n  const getTenantName = (tenantId: string) => {\n    const tenant = tenants.find(t => t.id === tenantId);\n    return tenant?.name || tenantId;\n  };\n\n  const calculateTotalMetrics = () => {\n    return metrics.reduce((totals, metric) => ({\n      staffCount: totals.staffCount + metric.staffCount,\n      clientCount: totals.clientCount + metric.clientCount,\n      reportCount: totals.reportCount + metric.reportCount,\n      scheduleItemCount: totals.scheduleItemCount + metric.scheduleItemCount,\n      activityCount: totals.activityCount + metric.activityCount,\n    }), {\n      staffCount: 0,\n      clientCount: 0,\n      reportCount: 0,\n      scheduleItemCount: 0,\n      activityCount: 0,\n    });\n  };\n\n  const calculateRevenue = (metric: BillingMetrics) => {\n    // Simplified pricing model\n    const basePrice = 50; // €50 per staff member per month\n    const activityPrice = 0.25; // €0.25 per activity\n    \n    return (metric.staffCount * basePrice) + (metric.activityCount * activityPrice);\n  };\n\n  const handleExportPDF = () => {\n    // Mock PDF export\n    const blob = new Blob(['Mock PDF content'], { type: 'application/pdf' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `factuur-${selectedPeriod.start}-${selectedPeriod.end}.pdf`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleExportCSV = () => {\n    // Mock CSV export\n    const csvContent = [\n      'Tenant,Medewerkers,Cliënten,Rapportages,Afspraken,Activiteiten,Omzet',\n      ...metrics.map(metric => \n        `${getTenantName(metric.tenantId)},${metric.staffCount},${metric.clientCount},${metric.reportCount},${metric.scheduleItemCount},${metric.activityCount},€${calculateRevenue(metric).toFixed(2)}`\n      )\n    ].join('\\n');\n\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `facturatie-${selectedPeriod.start}-${selectedPeriod.end}.csv`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const totals = calculateTotalMetrics();\n  const totalRevenue = metrics.reduce((sum, metric) => sum + calculateRevenue(metric), 0);\n\n  if (loading) {\n    return (\n      <Card>\n        <CardContent className=\"p-8 text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-2 text-gray-500\">Facturatie gegevens laden...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900\">Facturatie Overzicht</h2>\n          <p className=\"text-gray-600 mt-1\">\n            Automatische ratio berekeningen en export functionaliteit\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <Button variant=\"outline\" onClick={handleExportCSV}>\n            <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n            CSV Export\n          </Button>\n          <Button onClick={handleExportPDF}>\n            <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n            PDF Export\n          </Button>\n        </div>\n      </div>\n\n      {/* Period Selection */}\n      <Card>\n        <CardContent className=\"p-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4 items-end\">\n            <Input\n              type=\"date\"\n              label=\"Startdatum\"\n              value={selectedPeriod.start}\n              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, start: e.target.value }))}\n            />\n            <Input\n              type=\"date\"\n              label=\"Einddatum\"\n              value={selectedPeriod.end}\n              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, end: e.target.value }))}\n            />\n            <div className=\"text-sm text-gray-500\">\n              Periode: {formatDate(new Date(selectedPeriod.start))} - {formatDate(new Date(selectedPeriod.end))}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Summary Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-blue-100 rounded-lg\">\n                <UserGroupIcon className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Totaal Medewerkers</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{totals.staffCount}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-green-100 rounded-lg\">\n                <BuildingOfficeIcon className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Totaal Cliënten</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{totals.clientCount}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-purple-100 rounded-lg\">\n                <ChartBarIcon className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Totaal Activiteiten</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{totals.activityCount}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-yellow-100 rounded-lg\">\n                <CurrencyEuroIcon className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Totale Omzet</p>\n                <p className=\"text-2xl font-bold text-gray-900\">€{totalRevenue.toFixed(2)}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Tenant Breakdown */}\n      <Card>\n        <CardHeader>\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            Overzicht per Tenant\n          </h3>\n        </CardHeader>\n        <CardContent className=\"p-0\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Tenant\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Medewerkers\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Cliënten\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Rapportages\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Afspraken\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Activiteiten\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Omzet\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {metrics.map((metric) => (\n                  <tr key={metric.tenantId} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {getTenantName(metric.tenantId)}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {metric.tenantId}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {metric.staffCount}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {metric.clientCount}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {metric.reportCount}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {metric.scheduleItemCount}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {metric.activityCount}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                      €{calculateRevenue(metric).toFixed(2)}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Pricing Information */}\n      <Card>\n        <CardHeader>\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            Prijsmodel\n          </h3>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <h4 className=\"font-medium text-gray-900 mb-2\">Basis Tarieven</h4>\n              <ul className=\"space-y-1 text-sm text-gray-600\">\n                <li>• €50 per medewerker per maand</li>\n                <li>• €0.25 per activiteit</li>\n                <li>• Automatische berekening op basis van gebruik</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-medium text-gray-900 mb-2\">Activiteiten</h4>\n              <ul className=\"space-y-1 text-sm text-gray-600\">\n                <li>• Rapportages aanmaken/bijwerken</li>\n                <li>• Afspraken plannen/bijwerken</li>\n                <li>• Cliënt acties</li>\n                <li>• Systeem interacties</li>\n              </ul>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AAAA;AAAA;AAEA;;;AAdA;;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,OAAO,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,IAAI,GAAG,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC/F,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;YACA;QACF;oCAAG;QAAC;KAAe;IAEnB,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,WAAW,eAAe,KAAK;gBAC/B,SAAS,eAAe,GAAG;YAC7B;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,wBAA8B,OAAP;YACrD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,OAAO,CAAA,mBAAA,6BAAA,OAAQ,IAAI,KAAI;IACzB;IAEA,MAAM,wBAAwB;QAC5B,OAAO,QAAQ,MAAM,CAAC,CAAC,QAAQ,SAAW,CAAC;gBACzC,YAAY,OAAO,UAAU,GAAG,OAAO,UAAU;gBACjD,aAAa,OAAO,WAAW,GAAG,OAAO,WAAW;gBACpD,aAAa,OAAO,WAAW,GAAG,OAAO,WAAW;gBACpD,mBAAmB,OAAO,iBAAiB,GAAG,OAAO,iBAAiB;gBACtE,eAAe,OAAO,aAAa,GAAG,OAAO,aAAa;YAC5D,CAAC,GAAG;YACF,YAAY;YACZ,aAAa;YACb,aAAa;YACb,mBAAmB;YACnB,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,2BAA2B;QAC3B,MAAM,YAAY,IAAI,iCAAiC;QACvD,MAAM,gBAAgB,MAAM,qBAAqB;QAEjD,OAAO,AAAC,OAAO,UAAU,GAAG,YAAc,OAAO,aAAa,GAAG;IACnE;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAmB,EAAE;YAAE,MAAM;QAAkB;QACtE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,AAAC,WAAkC,OAAxB,eAAe,KAAK,EAAC,KAAsB,OAAnB,eAAe,GAAG,EAAC;QACnE,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,MAAM,aAAa;YACjB;eACG,QAAQ,GAAG,CAAC,CAAA,SACb,AAAC,GAAoC,OAAlC,cAAc,OAAO,QAAQ,GAAE,KAAwB,OAArB,OAAO,UAAU,EAAC,KAAyB,OAAtB,OAAO,WAAW,EAAC,KAAyB,OAAtB,OAAO,WAAW,EAAC,KAA+B,OAA5B,OAAO,iBAAiB,EAAC,KAA4B,OAAzB,OAAO,aAAa,EAAC,MAAwC,OAApC,iBAAiB,QAAQ,OAAO,CAAC;SAE/L,CAAC,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,AAAC,cAAqC,OAAxB,eAAe,KAAK,EAAC,KAAsB,OAAnB,eAAe,GAAG,EAAC;QACtE,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,SAAS;IACf,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,iBAAiB,SAAS;IAErF,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,0KAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,oOAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGhD,6LAAC,0KAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,6LAAC,oOAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOpD,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wKAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAM;gCACN,OAAO,eAAe,KAAK;gCAC3B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;;;;;;0CAEhF,6LAAC,wKAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAM;gCACN,OAAO,eAAe,GAAG;gCACzB,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,KAAK,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;;;;;;0CAE9E,6LAAC;gCAAI,WAAU;;oCAAwB;oCAC3B,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,eAAe,KAAK;oCAAG;oCAAI,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,eAAe,GAAG;;;;;;;;;;;;;;;;;;;;;;;0BAOvG,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,4NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM1E,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,sOAAA,CAAA,qBAAkB;4CAAC,WAAU;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7E,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAmC;oDAAE,aAAa,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjF,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;kCAItD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;;;;;;;;;;;;kDAKnG,6LAAC;wCAAM,WAAU;kDACd,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gDAAyB,WAAU;;kEAClC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;0EACZ,cAAc,OAAO,QAAQ;;;;;;0EAEhC,6LAAC;gEAAI,WAAU;0EACZ,OAAO,QAAQ;;;;;;;;;;;;kEAGpB,6LAAC;wDAAG,WAAU;kEACX,OAAO,UAAU;;;;;;kEAEpB,6LAAC;wDAAG,WAAU;kEACX,OAAO,WAAW;;;;;;kEAErB,6LAAC;wDAAG,WAAU;kEACX,OAAO,WAAW;;;;;;kEAErB,6LAAC;wDAAG,WAAU;kEACX,OAAO,iBAAiB;;;;;;kEAE3B,6LAAC;wDAAG,WAAU;kEACX,OAAO,aAAa;;;;;;kEAEvB,6LAAC;wDAAG,WAAU;;4DAAgE;4DAC1E,iBAAiB,QAAQ,OAAO,CAAC;;;;;;;;+CAzB9B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoCpC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;kCAItD,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GA1UwB;KAAA", "debugId": null}}, {"offset": {"line": 2127, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/app/facturatie/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { useAuthStore } from \"@/stores/authStore\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport BillingOverview from \"@/components/billing/BillingOverview\";\nimport { Badge } from \"@/components/ui\";\n\nexport default function FacturatiePage() {\n  const router = useRouter();\n  const { user } = useAuthStore();\n\n  useEffect(() => {\n    if (!user) {\n      router.push(\"/\");\n    } else if (user.role !== \"admin\") {\n      router.push(\"/dashboard\");\n    }\n  }, [user, router]);\n\n  if (!user || user.role !== \"admin\") {\n    return null;\n  }\n\n  return (\n    <AppLayout>\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Facturatie</h1>\n            <p className=\"text-gray-600 mt-1\">\n              Beheer facturatie en billing metrics\n            </p>\n          </div>\n          <Badge variant=\"info\">Admin Only</Badge>\n        </div>\n\n        <BillingOverview />\n      </div>\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,KAAK,IAAI,KAAK,SAAS;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAM;KAAO;IAEjB,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;QAClC,OAAO;IACT;IAEA,qBACE,6LAAC,4IAAA,CAAA,UAAS;kBACR,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAIpC,6LAAC,wKAAA,CAAA,QAAK;4BAAC,SAAQ;sCAAO;;;;;;;;;;;;8BAGxB,6LAAC,mJAAA,CAAA,UAAe;;;;;;;;;;;;;;;;AAIxB;GAjCwB;;QACP,qIAAA,CAAA,YAAS;QACP,6HAAA,CAAA,eAAY;;;KAFP", "debugId": null}}]}