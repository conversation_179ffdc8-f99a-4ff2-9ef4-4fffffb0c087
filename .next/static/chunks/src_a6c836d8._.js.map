{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/utils/index.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\n\n// Utility voor het combineren van CSS classes\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n// Date formatting utilities\nexport function formatDate(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString(locale, {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function formatDateTime(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleString(locale, {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\nexport function formatTime(date: Date | string, locale: string = 'nl-NL'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleTimeString(locale, {\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Status utilities\nexport function getStatusColor(status: string): string {\n  switch (status) {\n    case 'complete':\n    case 'completed':\n    case 'active':\n      return 'text-green-600 bg-green-100';\n    case 'partial':\n    case 'paused':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'missing':\n    case 'cancelled':\n      return 'text-red-600 bg-red-100';\n    case 'scheduled':\n      return 'text-blue-600 bg-blue-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getStatusText(status: string): string {\n  switch (status) {\n    case 'complete':\n      return 'Compleet';\n    case 'partial':\n      return 'Gedeeltelijk';\n    case 'missing':\n      return 'Ontbreekt';\n    case 'completed':\n      return 'Voltooid';\n    case 'active':\n      return 'Actief';\n    case 'paused':\n      return 'Gepauzeerd';\n    case 'scheduled':\n      return 'Gepland';\n    case 'cancelled':\n      return 'Geannuleerd';\n    default:\n      return status;\n  }\n}\n\nexport function getPriorityColor(priority: string): string {\n  switch (priority) {\n    case 'high':\n      return 'text-red-600 bg-red-100';\n    case 'medium':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'low':\n      return 'text-green-600 bg-green-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function getPriorityText(priority: string): string {\n  switch (priority) {\n    case 'high':\n      return 'Hoog';\n    case 'medium':\n      return 'Gemiddeld';\n    case 'low':\n      return 'Laag';\n    default:\n      return priority;\n  }\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidFQDN(email: string): boolean {\n  // Controleer of email een FQDN format heeft (<EMAIL>)\n  const fqdnRegex = /^[^\\s@]+@[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\\.[a-zA-Z]{2,}$/;\n  return fqdnRegex.test(email);\n}\n\n// Text utilities\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function capitalizeFirst(text: string): string {\n  return text.charAt(0).toUpperCase() + text.slice(1);\n}\n\n// Array utilities\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key];\n    const bVal = b[key];\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n    return 0;\n  });\n}\n\n// Local storage utilities\nexport function getFromStorage(key: string): string | null {\n  if (typeof window === 'undefined') return null;\n  try {\n    return localStorage.getItem(key);\n  } catch {\n    return null;\n  }\n}\n\nexport function setToStorage(key: string, value: string): void {\n  if (typeof window === 'undefined') return;\n  try {\n    localStorage.setItem(key, value);\n  } catch {\n    // Silently fail\n  }\n}\n\nexport function removeFromStorage(key: string): void {\n  if (typeof window === 'undefined') return;\n  try {\n    localStorage.removeItem(key);\n  } catch {\n    // Silently fail\n  }\n}\n\n// API utilities\nexport function buildQueryString(params: Record<string, any>): string {\n  const searchParams = new URLSearchParams();\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== '') {\n      if (value instanceof Date) {\n        searchParams.append(key, value.toISOString());\n      } else {\n        searchParams.append(key, String(value));\n      }\n    }\n  });\n  \n  return searchParams.toString();\n}\n\n// Error handling utilities\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'Er is een onbekende fout opgetreden';\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Generate unique ID\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAGO,SAAS,WAAW,IAAmB;QAAE,SAAA,iEAAiB;IAC/D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,QAAQ;QACxC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;QAAE,SAAA,iEAAiB;IACnE,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,cAAc,CAAC,QAAQ;QACpC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,IAAmB;QAAE,SAAA,iEAAiB;IAC/D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,QAAQ;QACxC,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,KAAa;IACvC,mEAAmE;IACnE,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;AACnD;AAGO,SAAS,QAAW,KAAU,EAAE,GAAY;IACjD,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY;QAAE,YAAA,iEAA4B;IAC9E,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,eAAe,GAAW;IACxC;;IACA,IAAI;QACF,OAAO,aAAa,OAAO,CAAC;IAC9B,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,aAAa,GAAW,EAAE,KAAa;IACrD;;IACA,IAAI;QACF,aAAa,OAAO,CAAC,KAAK;IAC5B,EAAE,UAAM;IACN,gBAAgB;IAClB;AACF;AAEO,SAAS,kBAAkB,GAAW;IAC3C;;IACA,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,UAAM;IACN,gBAAgB;IAClB;AACF;AAGO,SAAS,iBAAiB,MAA2B;IAC1D,MAAM,eAAe,IAAI;IAEzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC;YAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,iBAAiB,MAAM;gBACzB,aAAa,MAAM,CAAC,KAAK,MAAM,WAAW;YAC5C,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;IACF;IAEA,OAAO,aAAa,QAAQ;AAC9B;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Card.tsx"], "sourcesContent": ["import React from \"react\";\nimport { cn } from \"@/utils\";\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"healthcare-card healthcare-fade-in\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"healthcare-card-header\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"healthcare-card-content\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"px-6 py-4 border-t border-gray-200\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nCard.displayName = \"Card\";\nCardHeader.displayName = \"CardHeader\";\nCardContent.displayName = \"CardContent\";\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAkBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;;;AAKP,KAAK,WAAW,GAAG;AACnB,WAAW,WAAW,GAAG;AACzB,YAAY,WAAW,GAAG;AAC1B,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Button.tsx"], "sourcesContent": ["import React from \"react\";\nimport { cn } from \"@/utils\";\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"primary\" | \"secondary\" | \"outline\" | \"ghost\" | \"danger\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  (\n    {\n      className,\n      variant = \"primary\",\n      size = \"md\",\n      loading = false,\n      disabled,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = \"healthcare-btn healthcare-focus\";\n\n    const variants = {\n      primary: \"healthcare-btn-primary\",\n      secondary: \"healthcare-btn-secondary\",\n      outline: \"healthcare-btn-outline\",\n      ghost:\n        \"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 bg-transparent border-transparent rounded-xl\",\n      danger:\n        \"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 rounded-xl\",\n    };\n\n    const sizes = {\n      sm: \"px-4 py-2 text-sm\",\n      md: \"px-6 py-3 text-sm\",\n      lg: \"px-8 py-4 text-base\",\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAUE;QATA,EACE,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IAGD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OACE;QACF,QACE;IACJ;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from \"react\";\nimport { cn } from \"@/utils\";\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {\n  variant?: \"default\" | \"success\" | \"warning\" | \"danger\" | \"info\";\n  size?: \"sm\" | \"md\";\n  children: React.ReactNode;\n}\n\nconst Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(\n  (\n    { className, variant = \"default\", size = \"md\", children, ...props },\n    ref\n  ) => {\n    const baseClasses = \"healthcare-badge\";\n\n    const variants = {\n      default: \"bg-gray-100 text-gray-800\",\n      success: \"healthcare-badge-success\",\n      warning: \"healthcare-badge-warning\",\n      danger: \"healthcare-badge-error\",\n      info: \"healthcare-badge-info\",\n    };\n\n    const sizes = {\n      sm: \"px-3 py-1 text-xs\",\n      md: \"px-4 py-1.5 text-sm\",\n    };\n\n    return (\n      <span\n        ref={ref}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        {...props}\n      >\n        {children}\n      </span>\n    );\n  }\n);\n\nBadge.displayName = \"Badge\";\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,QAEE;QADA,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;IAGnE,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC1D,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/Input.tsx"], "sourcesContent": ["import React from \"react\";\nimport { cn } from \"@/utils\";\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, label, error, helperText, id, ...props }, ref) => {\n    const generatedId = React.useId();\n    const inputId = id || generatedId;\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <input\n          ref={ref}\n          id={inputId}\n          className={cn(\n            \"healthcare-input healthcare-focus\",\n            \"disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed\",\n            error && \"border-red-300 focus:border-red-500 focus:ring-red-500\",\n            className\n          )}\n          {...props}\n        />\n        {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = \"Input\";\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAQA,MAAM,sBAAQ,GAAA,6JAAA,CAAA,UAAK,CAAC,UAAU,SAC5B,QAAwD;QAAvD,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,OAAO;;IACpD,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,KAAK;IAC/B,MAAM,UAAU,MAAM;IAEtB,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,qCACA,0EACA,SAAS,0DACT;gBAED,GAAG,KAAK;;;;;;YAEV,uBAAS,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YACnD,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/components/ui/HealthcareStylePreview.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { <PERSON>, CardHeader, CardContent } from './Card';\nimport Button from './Button';\nimport Badge from './Badge';\nimport Input from './Input';\nimport { \n  HeartIcon, \n  UserGroupIcon, \n  CalendarDaysIcon,\n  DocumentTextIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  InformationCircleIcon\n} from '@heroicons/react/24/outline';\n\nexport default function HealthcareStylePreview() {\n  return (\n    <div className=\"healthcare-theme p-8 space-y-8 bg-gray-50 min-h-screen\">\n      <div className=\"max-w-6xl mx-auto\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            🏥 Healthcare Platform Styling\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            <PERSON><PERSON> professionele, vertrouwenwekkende styling voor het zorgportaal met zachte kleuren en rustige uitstraling.\n          </p>\n        </div>\n\n        {/* Color Palette */}\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <h2 className=\"text-2xl font-semibold text-gray-900\">Kleurenpalet</h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              {/* Primary Colors */}\n              <div>\n                <h3 className=\"font-medium text-gray-900 mb-3\">Primaire Kleuren (Zachte Blauw)</h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 rounded-lg bg-blue-100\"></div>\n                    <span className=\"text-sm\">Light Blue</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 rounded-lg bg-blue-500\"></div>\n                    <span className=\"text-sm\">Primary Blue</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 rounded-lg bg-blue-700\"></div>\n                    <span className=\"text-sm\">Dark Blue</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Secondary Colors */}\n              <div>\n                <h3 className=\"font-medium text-gray-900 mb-3\">Secundaire Kleuren (Zachte Groen)</h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 rounded-lg bg-green-100\"></div>\n                    <span className=\"text-sm\">Light Green</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 rounded-lg bg-green-500\"></div>\n                    <span className=\"text-sm\">Primary Green</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 rounded-lg bg-green-700\"></div>\n                    <span className=\"text-sm\">Dark Green</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Neutral Colors */}\n              <div>\n                <h3 className=\"font-medium text-gray-900 mb-3\">Neutrale Kleuren (Warme Grijs)</h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 rounded-lg bg-gray-100\"></div>\n                    <span className=\"text-sm\">Light Gray</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 rounded-lg bg-gray-500\"></div>\n                    <span className=\"text-sm\">Medium Gray</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 rounded-lg bg-gray-800\"></div>\n                    <span className=\"text-sm\">Dark Gray</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Buttons */}\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <h2 className=\"text-2xl font-semibold text-gray-900\">Buttons</h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              <div>\n                <h3 className=\"font-medium text-gray-900 mb-3\">Primary Buttons</h3>\n                <div className=\"space-y-3\">\n                  <Button variant=\"primary\" size=\"sm\">\n                    <HeartIcon className=\"w-4 h-4 mr-2\" />\n                    Klein\n                  </Button>\n                  <Button variant=\"primary\" size=\"md\">\n                    <UserGroupIcon className=\"w-5 h-5 mr-2\" />\n                    Medium\n                  </Button>\n                  <Button variant=\"primary\" size=\"lg\">\n                    <CalendarDaysIcon className=\"w-6 h-6 mr-2\" />\n                    Groot\n                  </Button>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"font-medium text-gray-900 mb-3\">Secondary Buttons</h3>\n                <div className=\"space-y-3\">\n                  <Button variant=\"secondary\" size=\"sm\">Secundair Klein</Button>\n                  <Button variant=\"secondary\" size=\"md\">Secundair Medium</Button>\n                  <Button variant=\"secondary\" size=\"lg\">Secundair Groot</Button>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"font-medium text-gray-900 mb-3\">Outline & Ghost</h3>\n                <div className=\"space-y-3\">\n                  <Button variant=\"outline\" size=\"md\">Outline Button</Button>\n                  <Button variant=\"ghost\" size=\"md\">Ghost Button</Button>\n                  <Button variant=\"danger\" size=\"md\">Danger Button</Button>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Badges */}\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <h2 className=\"text-2xl font-semibold text-gray-900\">Status Badges</h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex flex-wrap gap-4\">\n              <Badge variant=\"success\">\n                <CheckCircleIcon className=\"w-4 h-4 mr-1\" />\n                Actief\n              </Badge>\n              <Badge variant=\"warning\">\n                <ExclamationTriangleIcon className=\"w-4 h-4 mr-1\" />\n                Wachtend\n              </Badge>\n              <Badge variant=\"danger\">\n                <ExclamationTriangleIcon className=\"w-4 h-4 mr-1\" />\n                Inactief\n              </Badge>\n              <Badge variant=\"info\">\n                <InformationCircleIcon className=\"w-4 h-4 mr-1\" />\n                Informatie\n              </Badge>\n              <Badge variant=\"default\">Standaard</Badge>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Form Elements */}\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <h2 className=\"text-2xl font-semibold text-gray-900\">Formulier Elementen</h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                <Input \n                  label=\"Naam\" \n                  placeholder=\"Voer uw naam in\"\n                  helperText=\"Dit veld is verplicht\"\n                />\n                <Input \n                  label=\"Email\" \n                  type=\"email\"\n                  placeholder=\"<EMAIL>\"\n                />\n                <Input \n                  label=\"Telefoon\" \n                  type=\"tel\"\n                  placeholder=\"+31 6 12345678\"\n                />\n              </div>\n              <div className=\"space-y-4\">\n                <Input \n                  label=\"Organisatie\" \n                  placeholder=\"Zorgorganisatie naam\"\n                />\n                <Input \n                  label=\"Functie\" \n                  placeholder=\"Zorgverlener\"\n                />\n                <Input \n                  label=\"Fout voorbeeld\" \n                  placeholder=\"Dit veld heeft een fout\"\n                  error=\"Dit veld is verplicht\"\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Cards Showcase */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n          <Card>\n            <CardHeader>\n              <h3 className=\"text-lg font-semibold text-gray-900\">Cliënt Overzicht</h3>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Totaal Actief:</span>\n                  <Badge variant=\"success\">24</Badge>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Wachtend:</span>\n                  <Badge variant=\"warning\">3</Badge>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Inactief:</span>\n                  <Badge variant=\"danger\">1</Badge>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <h3 className=\"text-lg font-semibold text-gray-900\">Agenda Vandaag</h3>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  <span className=\"text-sm\">09:00 - Intake C001</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                  <span className=\"text-sm\">11:30 - Follow-up C003</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-2 h-2 bg-yellow-500 rounded-full\"></div>\n                  <span className=\"text-sm\">14:00 - Evaluatie C005</span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <h3 className=\"text-lg font-semibold text-gray-900\">Rapportages</h3>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Compleet:</span>\n                  <Badge variant=\"success\">18</Badge>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Gedeeltelijk:</span>\n                  <Badge variant=\"warning\">5</Badge>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Ontbreekt:</span>\n                  <Badge variant=\"danger\">2</Badge>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Typography */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-2xl font-semibold text-gray-900\">Typografie</h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div>\n                <h1 className=\"text-4xl font-bold text-gray-900\">Heading 1 - Hoofdtitel</h1>\n                <h2 className=\"text-3xl font-semibold text-gray-800\">Heading 2 - Sectietitel</h2>\n                <h3 className=\"text-2xl font-medium text-gray-700\">Heading 3 - Subsectie</h3>\n                <h4 className=\"text-xl font-medium text-gray-600\">Heading 4 - Kleine titel</h4>\n              </div>\n              <div>\n                <p className=\"text-base text-gray-900 mb-2\">\n                  <strong>Body Text Regular:</strong> Dit is de standaard body tekst voor het healthcare platform. \n                  De tekst is goed leesbaar en heeft voldoende contrast voor toegankelijkheid.\n                </p>\n                <p className=\"text-sm text-gray-600\">\n                  <strong>Small Text:</strong> Kleinere tekst voor bijkomende informatie, labels en helper tekst.\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAiBe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;sCAEvD,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAMhC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAMhC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStC,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;sCAEvD,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAU,MAAK;;0EAC7B,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGxC,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAU,MAAK;;0EAC7B,6LAAC,4NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAG5C,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAU,MAAK;;0EAC7B,6LAAC,kOAAA,CAAA,mBAAgB;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAMnD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAY,MAAK;kEAAK;;;;;;kEACtC,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAY,MAAK;kEAAK;;;;;;kEACtC,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAY,MAAK;kEAAK;;;;;;;;;;;;;;;;;;kDAI1C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAU,MAAK;kEAAK;;;;;;kEACpC,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAAK;;;;;;kEAClC,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAS,MAAK;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ7C,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;sCAEvD,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,UAAK;wCAAC,SAAQ;;0DACb,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG9C,6LAAC,oIAAA,CAAA,UAAK;wCAAC,SAAQ;;0DACb,6LAAC,gPAAA,CAAA,0BAAuB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGtD,6LAAC,oIAAA,CAAA,UAAK;wCAAC,SAAQ;;0DACb,6LAAC,gPAAA,CAAA,0BAAuB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGtD,6LAAC,oIAAA,CAAA,UAAK;wCAAC,SAAQ;;0DACb,6LAAC,4OAAA,CAAA,wBAAqB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpD,6LAAC,oIAAA,CAAA,UAAK;wCAAC,SAAQ;kDAAU;;;;;;;;;;;;;;;;;;;;;;;8BAM/B,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;sCAEvD,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,UAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,YAAW;;;;;;0DAEb,6LAAC,oIAAA,CAAA,UAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;;;;;;0DAEd,6LAAC,oIAAA,CAAA,UAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;;;;;;;;;;;;kDAGhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,UAAK;gDACJ,OAAM;gDACN,aAAY;;;;;;0DAEd,6LAAC,oIAAA,CAAA,UAAK;gDACJ,OAAM;gDACN,aAAY;;;;;;0DAEd,6LAAC,oIAAA,CAAA,UAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;8CAEtD,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,oIAAA,CAAA,UAAK;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,oIAAA,CAAA,UAAK;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,oIAAA,CAAA,UAAK;wDAAC,SAAQ;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;8CAEtD,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMlC,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;8CAEtD,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,oIAAA,CAAA,UAAK;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,oIAAA,CAAA,UAAK;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,oIAAA,CAAA,UAAK;wDAAC,SAAQ;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;sCAEvD,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;;;;;;;kDAEpD,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAA2B;;;;;;;0DAGrC,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;KAvSwB", "debugId": null}}, {"offset": {"line": 1848, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/app/style-preview/page.tsx"], "sourcesContent": ["'use client';\n\nimport HealthcareStylePreview from '@/components/ui/HealthcareStylePreview';\n\nexport default function StylePreviewPage() {\n  return <HealthcareStylePreview />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,6LAAC,qJAAA,CAAA,UAAsB;;;;;AAChC;KAFwB", "debugId": null}}]}