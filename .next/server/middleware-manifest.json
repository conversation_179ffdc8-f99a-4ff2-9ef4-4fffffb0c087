{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_18677c95._.js", "server/edge/chunks/[root-of-the-server]__9e019b0e._.js", "server/edge/chunks/edge-wrapper_5e6806d9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jzd+v3SSbdyOW+d8u4I99HDsi8CjGG81c1YJr2P5WrQ=", "__NEXT_PREVIEW_MODE_ID": "c6a4f3ebad99b58d889dcc4e4fe7ba44", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6ece2e72d40bded3f3db4e794d633c9fc04b1a99fdb684d21fdfe37a1399d289", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ec3d3c611cc4ae0a30ed79b7ce2411a07de56ffb441640fbe497d4e275a3b76e"}}}, "sortedMiddleware": ["/"], "functions": {}}