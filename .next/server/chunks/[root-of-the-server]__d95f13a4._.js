module.exports = {

"[project]/.next-internal/server/app/api/admin/activity-logs/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/mockAdminData.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "actionTypeMap": ()=>actionTypeMap,
    "getActionDisplayName": ()=>getActionDisplayName,
    "getActiveTenants": ()=>getActiveTenants,
    "getActiveUsers": ()=>getActiveUsers,
    "getActivityLogsByTenant": ()=>getActivityLogsByTenant,
    "getActivityLogsByUser": ()=>getActivityLogsByUser,
    "getBillingMetricsByTenant": ()=>getBillingMetricsByTenant,
    "getTenantById": ()=>getTenantById,
    "getUserById": ()=>getUserById,
    "getUsersByTenant": ()=>getUsersByTenant,
    "mockBillingMetrics": ()=>mockBillingMetrics,
    "mockExtendedActivityLogs": ()=>mockExtendedActivityLogs,
    "mockTenants": ()=>mockTenants,
    "mockUsers": ()=>mockUsers
});
const mockUsers = [
    {
        id: '1',
        email: '<EMAIL>',
        name: 'Admin Gebruiker',
        role: 'admin',
        tenantId: 'zorgorganisatie',
        isActive: true,
        lastLogin: new Date('2024-08-07T08:30:00'),
        mfaEnabled: true
    },
    {
        id: '2',
        email: '<EMAIL>',
        name: 'Zorgverlener Test',
        role: 'user',
        tenantId: 'zorgorganisatie',
        isActive: true,
        lastLogin: new Date('2024-08-07T09:15:00'),
        mfaEnabled: false
    },
    {
        id: '3',
        email: '<EMAIL>',
        name: 'Manager Zorg',
        role: 'user',
        tenantId: 'zorgorganisatie',
        isActive: true,
        lastLogin: new Date('2024-08-06T16:45:00'),
        mfaEnabled: true
    },
    {
        id: '4',
        email: '<EMAIL>',
        name: 'Medewerker Zorg',
        role: 'user',
        tenantId: 'zorgorganisatie',
        isActive: false,
        lastLogin: new Date('2024-07-15T14:20:00'),
        mfaEnabled: false
    },
    {
        id: '5',
        email: '<EMAIL>',
        name: 'Admin Andere Zorg',
        role: 'admin',
        tenantId: 'anderezorg',
        isActive: true,
        lastLogin: new Date('2024-08-07T07:00:00'),
        mfaEnabled: true
    }
];
const mockTenants = [
    {
        id: 'zorgorganisatie',
        name: 'Zorgorganisatie Nederland',
        domain: 'zorgorganisatie.nl',
        isActive: true,
        createdAt: new Date('2024-01-01'),
        settings: {
            language: 'nl',
            timezone: 'Europe/Amsterdam',
            features: {
                billing: true,
                reporting: true,
                scheduling: true
            }
        }
    },
    {
        id: 'anderezorg',
        name: 'Andere Zorg BV',
        domain: 'anderezorg.nl',
        isActive: true,
        createdAt: new Date('2024-03-15'),
        settings: {
            language: 'nl',
            timezone: 'Europe/Amsterdam',
            features: {
                billing: false,
                reporting: true,
                scheduling: true
            }
        }
    },
    {
        id: 'testzorg',
        name: 'Test Zorg Instelling',
        domain: 'testzorg.nl',
        isActive: false,
        createdAt: new Date('2024-02-10'),
        settings: {
            language: 'nl',
            timezone: 'Europe/Amsterdam',
            features: {
                billing: false,
                reporting: true,
                scheduling: false
            }
        }
    }
];
const mockExtendedActivityLogs = [
    {
        id: 'A001',
        tenantId: 'zorgorganisatie',
        userId: '2',
        action: 'CREATE_REPORT',
        resource: 'Report',
        resourceId: 'R001',
        timestamp: new Date('2024-08-07T14:30:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
        id: 'A002',
        tenantId: 'zorgorganisatie',
        userId: '1',
        action: 'UPDATE_USER',
        resource: 'User',
        resourceId: '2',
        timestamp: new Date('2024-08-07T09:15:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
        id: 'A003',
        tenantId: 'zorgorganisatie',
        userId: '2',
        action: 'CREATE_SCHEDULE',
        resource: 'ScheduleItem',
        resourceId: 'S002',
        timestamp: new Date('2024-08-07T11:20:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
        id: 'A004',
        tenantId: 'zorgorganisatie',
        userId: '1',
        action: 'LOGIN',
        resource: 'Auth',
        resourceId: '1',
        timestamp: new Date('2024-08-07T08:30:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
        id: 'A005',
        tenantId: 'zorgorganisatie',
        userId: '2',
        action: 'LOGIN',
        resource: 'Auth',
        resourceId: '2',
        timestamp: new Date('2024-08-07T09:15:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
        id: 'A006',
        tenantId: 'zorgorganisatie',
        userId: '3',
        action: 'CREATE_CLIENT',
        resource: 'Client',
        resourceId: 'C006',
        timestamp: new Date('2024-08-06T16:45:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    },
    {
        id: 'A007',
        tenantId: 'zorgorganisatie',
        userId: '2',
        action: 'UPDATE_REPORT',
        resource: 'Report',
        resourceId: 'R003',
        timestamp: new Date('2024-08-06T14:20:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
        id: 'A008',
        tenantId: 'anderezorg',
        userId: '5',
        action: 'LOGIN',
        resource: 'Auth',
        resourceId: '5',
        timestamp: new Date('2024-08-07T07:00:00'),
        ipAddress: '*********',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    }
];
const mockBillingMetrics = [
    {
        tenantId: 'zorgorganisatie',
        period: {
            start: new Date('2024-08-01'),
            end: new Date('2024-08-31')
        },
        staffCount: 4,
        clientCount: 5,
        reportCount: 15,
        scheduleItemCount: 8,
        activityCount: 45
    },
    {
        tenantId: 'anderezorg',
        period: {
            start: new Date('2024-08-01'),
            end: new Date('2024-08-31')
        },
        staffCount: 2,
        clientCount: 3,
        reportCount: 8,
        scheduleItemCount: 5,
        activityCount: 22
    }
];
function getUsersByTenant(tenantId) {
    return mockUsers.filter((user)=>user.tenantId === tenantId);
}
function getActiveUsers() {
    return mockUsers.filter((user)=>user.isActive);
}
function getActiveTenants() {
    return mockTenants.filter((tenant)=>tenant.isActive);
}
function getActivityLogsByTenant(tenantId) {
    return mockExtendedActivityLogs.filter((log)=>log.tenantId === tenantId);
}
function getActivityLogsByUser(userId) {
    return mockExtendedActivityLogs.filter((log)=>log.userId === userId);
}
function getBillingMetricsByTenant(tenantId) {
    return mockBillingMetrics.find((metrics)=>metrics.tenantId === tenantId);
}
function getUserById(id) {
    return mockUsers.find((user)=>user.id === id);
}
function getTenantById(id) {
    return mockTenants.find((tenant)=>tenant.id === id);
}
const actionTypeMap = {
    'LOGIN': 'Inloggen',
    'LOGOUT': 'Uitloggen',
    'CREATE_CLIENT': 'Cliënt aangemaakt',
    'UPDATE_CLIENT': 'Cliënt bijgewerkt',
    'DELETE_CLIENT': 'Cliënt verwijderd',
    'CREATE_REPORT': 'Rapportage aangemaakt',
    'UPDATE_REPORT': 'Rapportage bijgewerkt',
    'DELETE_REPORT': 'Rapportage verwijderd',
    'CREATE_SCHEDULE': 'Afspraak gepland',
    'UPDATE_SCHEDULE': 'Afspraak bijgewerkt',
    'DELETE_SCHEDULE': 'Afspraak geannuleerd',
    'CREATE_USER': 'Gebruiker aangemaakt',
    'UPDATE_USER': 'Gebruiker bijgewerkt',
    'DELETE_USER': 'Gebruiker verwijderd',
    'CREATE_GOAL': 'Zorgdoel aangemaakt',
    'UPDATE_GOAL': 'Zorgdoel bijgewerkt',
    'DELETE_GOAL': 'Zorgdoel verwijderd'
};
function getActionDisplayName(action) {
    return actionTypeMap[action] || action;
}
}),
"[project]/src/app/api/admin/activity-logs/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockAdminData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mockAdminData.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const tenantId = searchParams.get('tenantId');
        const userId = searchParams.get('userId');
        const action = searchParams.get('action');
        const resource = searchParams.get('resource');
        const dateFrom = searchParams.get('dateFrom');
        const dateTo = searchParams.get('dateTo');
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '20');
        let logs = [
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockAdminData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockExtendedActivityLogs"]
        ];
        // Filter by tenant
        if (tenantId) {
            logs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockAdminData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getActivityLogsByTenant"])(tenantId);
        }
        // Filter by user
        if (userId) {
            logs = logs.filter((log)=>log.userId === userId);
        }
        // Filter by action
        if (action) {
            logs = logs.filter((log)=>log.action === action);
        }
        // Filter by resource
        if (resource) {
            logs = logs.filter((log)=>log.resource === resource);
        }
        // Filter by date range
        if (dateFrom) {
            const fromDate = new Date(dateFrom);
            logs = logs.filter((log)=>new Date(log.timestamp) >= fromDate);
        }
        if (dateTo) {
            const toDate = new Date(dateTo);
            toDate.setHours(23, 59, 59, 999);
            logs = logs.filter((log)=>new Date(log.timestamp) <= toDate);
        }
        // Sort by timestamp (newest first)
        logs.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        // Add user information and action display names
        const enrichedLogs = logs.map((log)=>{
            const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockAdminData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserById"])(log.userId);
            return {
                ...log,
                userName: user?.name || 'Onbekende gebruiker',
                userEmail: user?.email || 'Onbekend',
                actionDisplayName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockAdminData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getActionDisplayName"])(log.action)
            };
        });
        // Pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedLogs = enrichedLogs.slice(startIndex, endIndex);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: paginatedLogs,
            pagination: {
                page,
                limit,
                total: enrichedLogs.length,
                totalPages: Math.ceil(enrichedLogs.length / limit)
            }
        });
    } catch (error) {
        console.error('Activity logs API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: 'Er is een serverfout opgetreden'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d95f13a4._.js.map