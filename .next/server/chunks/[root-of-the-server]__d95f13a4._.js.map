{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/lib/mockAdminData.ts"], "sourcesContent": ["import { User, Tenant, ActivityLog, BillingMetrics } from '@/types';\n\n// Mock Users Data (extended)\nexport const mockUsers: User[] = [\n  {\n    id: '1',\n    email: '<EMAIL>',\n    name: '<PERSON><PERSON>',\n    role: 'admin',\n    tenantId: 'zorgorganisatie',\n    isActive: true,\n    lastLogin: new Date('2024-08-07T08:30:00'),\n    mfaEnabled: true,\n  },\n  {\n    id: '2',\n    email: '<EMAIL>',\n    name: 'Zorgverlener Test',\n    role: 'user',\n    tenantId: 'zorgorganisatie',\n    isActive: true,\n    lastLogin: new Date('2024-08-07T09:15:00'),\n    mfaEnabled: false,\n  },\n  {\n    id: '3',\n    email: '<EMAIL>',\n    name: 'Manager Zorg',\n    role: 'user',\n    tenantId: 'zorgorganisatie',\n    isActive: true,\n    lastLogin: new Date('2024-08-06T16:45:00'),\n    mfaEnabled: true,\n  },\n  {\n    id: '4',\n    email: '<EMAIL>',\n    name: 'Medewerker Zorg',\n    role: 'user',\n    tenantId: 'zorgorganisatie',\n    isActive: false,\n    lastLogin: new Date('2024-07-15T14:20:00'),\n    mfaEnabled: false,\n  },\n  {\n    id: '5',\n    email: '<EMAIL>',\n    name: 'Admin Andere Zorg',\n    role: 'admin',\n    tenantId: 'anderezorg',\n    isActive: true,\n    lastLogin: new Date('2024-08-07T07:00:00'),\n    mfaEnabled: true,\n  },\n];\n\n// Mock Tenants Data\nexport const mockTenants: Tenant[] = [\n  {\n    id: 'zorgorganisatie',\n    name: 'Zorgorganisatie Nederland',\n    domain: 'zorgorganisatie.nl',\n    isActive: true,\n    createdAt: new Date('2024-01-01'),\n    settings: {\n      language: 'nl',\n      timezone: 'Europe/Amsterdam',\n      features: {\n        billing: true,\n        reporting: true,\n        scheduling: true,\n      },\n    },\n  },\n  {\n    id: 'anderezorg',\n    name: 'Andere Zorg BV',\n    domain: 'anderezorg.nl',\n    isActive: true,\n    createdAt: new Date('2024-03-15'),\n    settings: {\n      language: 'nl',\n      timezone: 'Europe/Amsterdam',\n      features: {\n        billing: false,\n        reporting: true,\n        scheduling: true,\n      },\n    },\n  },\n  {\n    id: 'testzorg',\n    name: 'Test Zorg Instelling',\n    domain: 'testzorg.nl',\n    isActive: false,\n    createdAt: new Date('2024-02-10'),\n    settings: {\n      language: 'nl',\n      timezone: 'Europe/Amsterdam',\n      features: {\n        billing: false,\n        reporting: true,\n        scheduling: false,\n      },\n    },\n  },\n];\n\n// Extended Activity Logs\nexport const mockExtendedActivityLogs: ActivityLog[] = [\n  {\n    id: 'A001',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'CREATE_REPORT',\n    resource: 'Report',\n    resourceId: 'R001',\n    timestamp: new Date('2024-08-07T14:30:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A002',\n    tenantId: 'zorgorganisatie',\n    userId: '1',\n    action: 'UPDATE_USER',\n    resource: 'User',\n    resourceId: '2',\n    timestamp: new Date('2024-08-07T09:15:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A003',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'CREATE_SCHEDULE',\n    resource: 'ScheduleItem',\n    resourceId: 'S002',\n    timestamp: new Date('2024-08-07T11:20:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A004',\n    tenantId: 'zorgorganisatie',\n    userId: '1',\n    action: 'LOGIN',\n    resource: 'Auth',\n    resourceId: '1',\n    timestamp: new Date('2024-08-07T08:30:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A005',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'LOGIN',\n    resource: 'Auth',\n    resourceId: '2',\n    timestamp: new Date('2024-08-07T09:15:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A006',\n    tenantId: 'zorgorganisatie',\n    userId: '3',\n    action: 'CREATE_CLIENT',\n    resource: 'Client',\n    resourceId: 'C006',\n    timestamp: new Date('2024-08-06T16:45:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n  },\n  {\n    id: 'A007',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'UPDATE_REPORT',\n    resource: 'Report',\n    resourceId: 'R003',\n    timestamp: new Date('2024-08-06T14:20:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A008',\n    tenantId: 'anderezorg',\n    userId: '5',\n    action: 'LOGIN',\n    resource: 'Auth',\n    resourceId: '5',\n    timestamp: new Date('2024-08-07T07:00:00'),\n    ipAddress: '*********',\n    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n  },\n];\n\n// Mock Billing Metrics\nexport const mockBillingMetrics: BillingMetrics[] = [\n  {\n    tenantId: 'zorgorganisatie',\n    period: {\n      start: new Date('2024-08-01'),\n      end: new Date('2024-08-31'),\n    },\n    staffCount: 4,\n    clientCount: 5,\n    reportCount: 15,\n    scheduleItemCount: 8,\n    activityCount: 45,\n  },\n  {\n    tenantId: 'anderezorg',\n    period: {\n      start: new Date('2024-08-01'),\n      end: new Date('2024-08-31'),\n    },\n    staffCount: 2,\n    clientCount: 3,\n    reportCount: 8,\n    scheduleItemCount: 5,\n    activityCount: 22,\n  },\n];\n\n// Helper functions\nexport function getUsersByTenant(tenantId: string): User[] {\n  return mockUsers.filter(user => user.tenantId === tenantId);\n}\n\nexport function getActiveUsers(): User[] {\n  return mockUsers.filter(user => user.isActive);\n}\n\nexport function getActiveTenants(): Tenant[] {\n  return mockTenants.filter(tenant => tenant.isActive);\n}\n\nexport function getActivityLogsByTenant(tenantId: string): ActivityLog[] {\n  return mockExtendedActivityLogs.filter(log => log.tenantId === tenantId);\n}\n\nexport function getActivityLogsByUser(userId: string): ActivityLog[] {\n  return mockExtendedActivityLogs.filter(log => log.userId === userId);\n}\n\nexport function getBillingMetricsByTenant(tenantId: string): BillingMetrics | undefined {\n  return mockBillingMetrics.find(metrics => metrics.tenantId === tenantId);\n}\n\nexport function getUserById(id: string): User | undefined {\n  return mockUsers.find(user => user.id === id);\n}\n\nexport function getTenantById(id: string): Tenant | undefined {\n  return mockTenants.find(tenant => tenant.id === id);\n}\n\n// Action type mappings for display\nexport const actionTypeMap: Record<string, string> = {\n  'LOGIN': 'Inloggen',\n  'LOGOUT': 'Uitloggen',\n  'CREATE_CLIENT': 'Cliënt aangemaakt',\n  'UPDATE_CLIENT': 'Cliënt bijgewerkt',\n  'DELETE_CLIENT': 'Cliënt verwijderd',\n  'CREATE_REPORT': 'Rapportage aangemaakt',\n  'UPDATE_REPORT': 'Rapportage bijgewerkt',\n  'DELETE_REPORT': 'Rapportage verwijderd',\n  'CREATE_SCHEDULE': 'Afspraak gepland',\n  'UPDATE_SCHEDULE': 'Afspraak bijgewerkt',\n  'DELETE_SCHEDULE': 'Afspraak geannuleerd',\n  'CREATE_USER': 'Gebruiker aangemaakt',\n  'UPDATE_USER': 'Gebruiker bijgewerkt',\n  'DELETE_USER': 'Gebruiker verwijderd',\n  'CREATE_GOAL': 'Zorgdoel aangemaakt',\n  'UPDATE_GOAL': 'Zorgdoel bijgewerkt',\n  'DELETE_GOAL': 'Zorgdoel verwijderd',\n};\n\nexport function getActionDisplayName(action: string): string {\n  return actionTypeMap[action] || action;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAGO,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;CACD;AAGM,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,UAAU;YACR,UAAU;YACV,UAAU;YACV,UAAU;gBACR,SAAS;gBACT,WAAW;gBACX,YAAY;YACd;QACF;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,UAAU;YACR,UAAU;YACV,UAAU;YACV,UAAU;gBACR,SAAS;gBACT,WAAW;gBACX,YAAY;YACd;QACF;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,UAAU;YACR,UAAU;YACV,UAAU;YACV,UAAU;gBACR,SAAS;gBACT,WAAW;gBACX,YAAY;YACd;QACF;IACF;CACD;AAGM,MAAM,2BAA0C;IACrD;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;CACD;AAGM,MAAM,qBAAuC;IAClD;QACE,UAAU;QACV,QAAQ;YACN,OAAO,IAAI,KAAK;YAChB,KAAK,IAAI,KAAK;QAChB;QACA,YAAY;QACZ,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,eAAe;IACjB;IACA;QACE,UAAU;QACV,QAAQ;YACN,OAAO,IAAI,KAAK;YAChB,KAAK,IAAI,KAAK;QAChB;QACA,YAAY;QACZ,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,eAAe;IACjB;CACD;AAGM,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AACpD;AAEO,SAAS;IACd,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAC/C;AAEO,SAAS;IACd,OAAO,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ;AACrD;AAEO,SAAS,wBAAwB,QAAgB;IACtD,OAAO,yBAAyB,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;AACjE;AAEO,SAAS,sBAAsB,MAAc;IAClD,OAAO,yBAAyB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;AAC/D;AAEO,SAAS,0BAA0B,QAAgB;IACxD,OAAO,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACjE;AAEO,SAAS,YAAY,EAAU;IACpC,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,SAAS,cAAc,EAAU;IACtC,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAClD;AAGO,MAAM,gBAAwC;IACnD,SAAS;IACT,UAAU;IACV,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IACnB,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;AACjB;AAEO,SAAS,qBAAqB,MAAc;IACjD,OAAO,aAAa,CAAC,OAAO,IAAI;AAClC", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/app/api/admin/activity-logs/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { \n  mockExtendedActivityLogs, \n  getActivityLogsByTenant, \n  getActivityLogsByUser,\n  getUserById,\n  getActionDisplayName\n} from '@/lib/mockAdminData';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const tenantId = searchParams.get('tenantId');\n    const userId = searchParams.get('userId');\n    const action = searchParams.get('action');\n    const resource = searchParams.get('resource');\n    const dateFrom = searchParams.get('dateFrom');\n    const dateTo = searchParams.get('dateTo');\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '20');\n\n    let logs = [...mockExtendedActivityLogs];\n\n    // Filter by tenant\n    if (tenantId) {\n      logs = getActivityLogsByTenant(tenantId);\n    }\n\n    // Filter by user\n    if (userId) {\n      logs = logs.filter(log => log.userId === userId);\n    }\n\n    // Filter by action\n    if (action) {\n      logs = logs.filter(log => log.action === action);\n    }\n\n    // Filter by resource\n    if (resource) {\n      logs = logs.filter(log => log.resource === resource);\n    }\n\n    // Filter by date range\n    if (dateFrom) {\n      const fromDate = new Date(dateFrom);\n      logs = logs.filter(log => new Date(log.timestamp) >= fromDate);\n    }\n\n    if (dateTo) {\n      const toDate = new Date(dateTo);\n      toDate.setHours(23, 59, 59, 999);\n      logs = logs.filter(log => new Date(log.timestamp) <= toDate);\n    }\n\n    // Sort by timestamp (newest first)\n    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n\n    // Add user information and action display names\n    const enrichedLogs = logs.map(log => {\n      const user = getUserById(log.userId);\n      return {\n        ...log,\n        userName: user?.name || 'Onbekende gebruiker',\n        userEmail: user?.email || 'Onbekend',\n        actionDisplayName: getActionDisplayName(log.action),\n      };\n    });\n\n    // Pagination\n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n    const paginatedLogs = enrichedLogs.slice(startIndex, endIndex);\n\n    return NextResponse.json({\n      success: true,\n      data: paginatedLogs,\n      pagination: {\n        page,\n        limit,\n        total: enrichedLogs.length,\n        totalPages: Math.ceil(enrichedLogs.length / limit),\n      },\n    });\n\n  } catch (error) {\n    console.error('Activity logs API error:', error);\n    return NextResponse.json(\n      { success: false, message: 'Er is een serverfout opgetreden' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAQO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,IAAI,OAAO;eAAI,6HAAA,CAAA,2BAAwB;SAAC;QAExC,mBAAmB;QACnB,IAAI,UAAU;YACZ,OAAO,CAAA,GAAA,6HAAA,CAAA,0BAAuB,AAAD,EAAE;QACjC;QAEA,iBAAiB;QACjB,IAAI,QAAQ;YACV,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;QAC3C;QAEA,mBAAmB;QACnB,IAAI,QAAQ;YACV,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;QAC3C;QAEA,qBAAqB;QACrB,IAAI,UAAU;YACZ,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;QAC7C;QAEA,uBAAuB;QACvB,IAAI,UAAU;YACZ,MAAM,WAAW,IAAI,KAAK;YAC1B,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,KAAK,IAAI,SAAS,KAAK;QACvD;QAEA,IAAI,QAAQ;YACV,MAAM,SAAS,IAAI,KAAK;YACxB,OAAO,QAAQ,CAAC,IAAI,IAAI,IAAI;YAC5B,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,KAAK,IAAI,SAAS,KAAK;QACvD;QAEA,mCAAmC;QACnC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAEnF,gDAAgD;QAChD,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;YAC5B,MAAM,OAAO,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE,IAAI,MAAM;YACnC,OAAO;gBACL,GAAG,GAAG;gBACN,UAAU,MAAM,QAAQ;gBACxB,WAAW,MAAM,SAAS;gBAC1B,mBAAmB,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,MAAM;YACpD;QACF;QAEA,aAAa;QACb,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,gBAAgB,aAAa,KAAK,CAAC,YAAY;QAErD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA,OAAO,aAAa,MAAM;gBAC1B,YAAY,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG;YAC9C;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAkC,GAC7D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}