{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/lib/mockAdminData.ts"], "sourcesContent": ["import { User, Tenant, ActivityLog, BillingMetrics } from '@/types';\n\n// Mock Users Data (extended)\nexport const mockUsers: User[] = [\n  {\n    id: '1',\n    email: '<EMAIL>',\n    name: '<PERSON><PERSON>',\n    role: 'admin',\n    tenantId: 'zorgorganisatie',\n    isActive: true,\n    lastLogin: new Date('2024-08-07T08:30:00'),\n    mfaEnabled: true,\n  },\n  {\n    id: '2',\n    email: '<EMAIL>',\n    name: 'Zorgverlener Test',\n    role: 'user',\n    tenantId: 'zorgorganisatie',\n    isActive: true,\n    lastLogin: new Date('2024-08-07T09:15:00'),\n    mfaEnabled: false,\n  },\n  {\n    id: '3',\n    email: '<EMAIL>',\n    name: 'Manager Zorg',\n    role: 'user',\n    tenantId: 'zorgorganisatie',\n    isActive: true,\n    lastLogin: new Date('2024-08-06T16:45:00'),\n    mfaEnabled: true,\n  },\n  {\n    id: '4',\n    email: '<EMAIL>',\n    name: 'Medewerker Zorg',\n    role: 'user',\n    tenantId: 'zorgorganisatie',\n    isActive: false,\n    lastLogin: new Date('2024-07-15T14:20:00'),\n    mfaEnabled: false,\n  },\n  {\n    id: '5',\n    email: '<EMAIL>',\n    name: 'Admin Andere Zorg',\n    role: 'admin',\n    tenantId: 'anderezorg',\n    isActive: true,\n    lastLogin: new Date('2024-08-07T07:00:00'),\n    mfaEnabled: true,\n  },\n];\n\n// Mock Tenants Data\nexport const mockTenants: Tenant[] = [\n  {\n    id: 'zorgorganisatie',\n    name: 'Zorgorganisatie Nederland',\n    domain: 'zorgorganisatie.nl',\n    isActive: true,\n    createdAt: new Date('2024-01-01'),\n    settings: {\n      language: 'nl',\n      timezone: 'Europe/Amsterdam',\n      features: {\n        billing: true,\n        reporting: true,\n        scheduling: true,\n      },\n    },\n  },\n  {\n    id: 'anderezorg',\n    name: 'Andere Zorg BV',\n    domain: 'anderezorg.nl',\n    isActive: true,\n    createdAt: new Date('2024-03-15'),\n    settings: {\n      language: 'nl',\n      timezone: 'Europe/Amsterdam',\n      features: {\n        billing: false,\n        reporting: true,\n        scheduling: true,\n      },\n    },\n  },\n  {\n    id: 'testzorg',\n    name: 'Test Zorg Instelling',\n    domain: 'testzorg.nl',\n    isActive: false,\n    createdAt: new Date('2024-02-10'),\n    settings: {\n      language: 'nl',\n      timezone: 'Europe/Amsterdam',\n      features: {\n        billing: false,\n        reporting: true,\n        scheduling: false,\n      },\n    },\n  },\n];\n\n// Extended Activity Logs\nexport const mockExtendedActivityLogs: ActivityLog[] = [\n  {\n    id: 'A001',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'CREATE_REPORT',\n    resource: 'Report',\n    resourceId: 'R001',\n    timestamp: new Date('2024-08-07T14:30:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A002',\n    tenantId: 'zorgorganisatie',\n    userId: '1',\n    action: 'UPDATE_USER',\n    resource: 'User',\n    resourceId: '2',\n    timestamp: new Date('2024-08-07T09:15:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A003',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'CREATE_SCHEDULE',\n    resource: 'ScheduleItem',\n    resourceId: 'S002',\n    timestamp: new Date('2024-08-07T11:20:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A004',\n    tenantId: 'zorgorganisatie',\n    userId: '1',\n    action: 'LOGIN',\n    resource: 'Auth',\n    resourceId: '1',\n    timestamp: new Date('2024-08-07T08:30:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A005',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'LOGIN',\n    resource: 'Auth',\n    resourceId: '2',\n    timestamp: new Date('2024-08-07T09:15:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A006',\n    tenantId: 'zorgorganisatie',\n    userId: '3',\n    action: 'CREATE_CLIENT',\n    resource: 'Client',\n    resourceId: 'C006',\n    timestamp: new Date('2024-08-06T16:45:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n  },\n  {\n    id: 'A007',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'UPDATE_REPORT',\n    resource: 'Report',\n    resourceId: 'R003',\n    timestamp: new Date('2024-08-06T14:20:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A008',\n    tenantId: 'anderezorg',\n    userId: '5',\n    action: 'LOGIN',\n    resource: 'Auth',\n    resourceId: '5',\n    timestamp: new Date('2024-08-07T07:00:00'),\n    ipAddress: '*********',\n    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n  },\n];\n\n// Mock Billing Metrics\nexport const mockBillingMetrics: BillingMetrics[] = [\n  {\n    tenantId: 'zorgorganisatie',\n    period: {\n      start: new Date('2024-08-01'),\n      end: new Date('2024-08-31'),\n    },\n    staffCount: 4,\n    clientCount: 5,\n    reportCount: 15,\n    scheduleItemCount: 8,\n    activityCount: 45,\n  },\n  {\n    tenantId: 'anderezorg',\n    period: {\n      start: new Date('2024-08-01'),\n      end: new Date('2024-08-31'),\n    },\n    staffCount: 2,\n    clientCount: 3,\n    reportCount: 8,\n    scheduleItemCount: 5,\n    activityCount: 22,\n  },\n];\n\n// Helper functions\nexport function getUsersByTenant(tenantId: string): User[] {\n  return mockUsers.filter(user => user.tenantId === tenantId);\n}\n\nexport function getActiveUsers(): User[] {\n  return mockUsers.filter(user => user.isActive);\n}\n\nexport function getActiveTenants(): Tenant[] {\n  return mockTenants.filter(tenant => tenant.isActive);\n}\n\nexport function getActivityLogsByTenant(tenantId: string): ActivityLog[] {\n  return mockExtendedActivityLogs.filter(log => log.tenantId === tenantId);\n}\n\nexport function getActivityLogsByUser(userId: string): ActivityLog[] {\n  return mockExtendedActivityLogs.filter(log => log.userId === userId);\n}\n\nexport function getBillingMetricsByTenant(tenantId: string): BillingMetrics | undefined {\n  return mockBillingMetrics.find(metrics => metrics.tenantId === tenantId);\n}\n\nexport function getUserById(id: string): User | undefined {\n  return mockUsers.find(user => user.id === id);\n}\n\nexport function getTenantById(id: string): Tenant | undefined {\n  return mockTenants.find(tenant => tenant.id === id);\n}\n\n// Action type mappings for display\nexport const actionTypeMap: Record<string, string> = {\n  'LOGIN': 'Inloggen',\n  'LOGOUT': 'Uitloggen',\n  'CREATE_CLIENT': 'Cliënt aangemaakt',\n  'UPDATE_CLIENT': 'Cliënt bijgewerkt',\n  'DELETE_CLIENT': 'Cliënt verwijderd',\n  'CREATE_REPORT': 'Rapportage aangemaakt',\n  'UPDATE_REPORT': 'Rapportage bijgewerkt',\n  'DELETE_REPORT': 'Rapportage verwijderd',\n  'CREATE_SCHEDULE': 'Afspraak gepland',\n  'UPDATE_SCHEDULE': 'Afspraak bijgewerkt',\n  'DELETE_SCHEDULE': 'Afspraak geannuleerd',\n  'CREATE_USER': 'Gebruiker aangemaakt',\n  'UPDATE_USER': 'Gebruiker bijgewerkt',\n  'DELETE_USER': 'Gebruiker verwijderd',\n  'CREATE_GOAL': 'Zorgdoel aangemaakt',\n  'UPDATE_GOAL': 'Zorgdoel bijgewerkt',\n  'DELETE_GOAL': 'Zorgdoel verwijderd',\n};\n\nexport function getActionDisplayName(action: string): string {\n  return actionTypeMap[action] || action;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAGO,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;CACD;AAGM,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,UAAU;YACR,UAAU;YACV,UAAU;YACV,UAAU;gBACR,SAAS;gBACT,WAAW;gBACX,YAAY;YACd;QACF;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,UAAU;YACR,UAAU;YACV,UAAU;YACV,UAAU;gBACR,SAAS;gBACT,WAAW;gBACX,YAAY;YACd;QACF;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,UAAU;YACR,UAAU;YACV,UAAU;YACV,UAAU;gBACR,SAAS;gBACT,WAAW;gBACX,YAAY;YACd;QACF;IACF;CACD;AAGM,MAAM,2BAA0C;IACrD;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;CACD;AAGM,MAAM,qBAAuC;IAClD;QACE,UAAU;QACV,QAAQ;YACN,OAAO,IAAI,KAAK;YAChB,KAAK,IAAI,KAAK;QAChB;QACA,YAAY;QACZ,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,eAAe;IACjB;IACA;QACE,UAAU;QACV,QAAQ;YACN,OAAO,IAAI,KAAK;YAChB,KAAK,IAAI,KAAK;QAChB;QACA,YAAY;QACZ,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,eAAe;IACjB;CACD;AAGM,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AACpD;AAEO,SAAS;IACd,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAC/C;AAEO,SAAS;IACd,OAAO,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ;AACrD;AAEO,SAAS,wBAAwB,QAAgB;IACtD,OAAO,yBAAyB,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;AACjE;AAEO,SAAS,sBAAsB,MAAc;IAClD,OAAO,yBAAyB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;AAC/D;AAEO,SAAS,0BAA0B,QAAgB;IACxD,OAAO,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACjE;AAEO,SAAS,YAAY,EAAU;IACpC,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,SAAS,cAAc,EAAU;IACtC,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAClD;AAGO,MAAM,gBAAwC;IACnD,SAAS;IACT,UAAU;IACV,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IACnB,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;AACjB;AAEO,SAAS,qBAAqB,MAAc;IACjD,OAAO,aAAa,CAAC,OAAO,IAAI;AAClC", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/lib/mockData.ts"], "sourcesContent": ["import { Client, CareGoal, Report, ScheduleItem, User, ActivityLog } from '@/types';\n\n// Mock Clients Data\nexport const mockClients: Client[] = [\n  {\n    id: 'C001',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C001',\n    isActive: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-08-01'),\n  },\n  {\n    id: 'C002',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C002',\n    isActive: true,\n    createdAt: new Date('2024-02-20'),\n    updatedAt: new Date('2024-07-28'),\n  },\n  {\n    id: 'C003',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C003',\n    isActive: true,\n    createdAt: new Date('2024-03-10'),\n    updatedAt: new Date('2024-08-05'),\n  },\n  {\n    id: 'C004',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C004',\n    isActive: false,\n    createdAt: new Date('2024-01-05'),\n    updatedAt: new Date('2024-06-15'),\n  },\n  {\n    id: 'C005',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C005',\n    isActive: true,\n    createdAt: new Date('2024-04-12'),\n    updatedAt: new Date('2024-08-03'),\n  },\n];\n\n// Mock Care Goals Data\nexport const mockCareGoals: CareGoal[] = [\n  {\n    id: 'G001',\n    clientId: 'C001',\n    tenantId: 'zorgorganisatie',\n    title: 'Mobiliteit verbeteren',\n    description: 'Dagelijkse oefeningen voor het verbeteren van de mobiliteit en balans.',\n    status: 'active',\n    priority: 'high',\n    startDate: new Date('2024-07-01'),\n    endDate: new Date('2024-09-01'),\n    createdBy: '2',\n    createdAt: new Date('2024-06-28'),\n    updatedAt: new Date('2024-08-01'),\n  },\n  {\n    id: 'G002',\n    clientId: 'C001',\n    tenantId: 'zorgorganisatie',\n    title: 'Medicatie compliance',\n    description: 'Zorgen voor correcte inname van voorgeschreven medicatie.',\n    status: 'completed',\n    priority: 'medium',\n    startDate: new Date('2024-06-01'),\n    endDate: new Date('2024-07-31'),\n    createdBy: '2',\n    createdAt: new Date('2024-05-28'),\n    updatedAt: new Date('2024-07-31'),\n  },\n  {\n    id: 'G003',\n    clientId: 'C002',\n    tenantId: 'zorgorganisatie',\n    title: 'Sociale activiteiten',\n    description: 'Deelname aan groepsactiviteiten ter bevordering van sociale interactie.',\n    status: 'active',\n    priority: 'medium',\n    startDate: new Date('2024-07-15'),\n    endDate: new Date('2024-10-15'),\n    createdBy: '2',\n    createdAt: new Date('2024-07-10'),\n    updatedAt: new Date('2024-08-05'),\n  },\n  {\n    id: 'G004',\n    clientId: 'C003',\n    tenantId: 'zorgorganisatie',\n    title: 'Voeding optimaliseren',\n    description: 'Verbetering van voedingspatroon en gewichtsbeheersing.',\n    status: 'paused',\n    priority: 'low',\n    startDate: new Date('2024-06-01'),\n    createdBy: '2',\n    createdAt: new Date('2024-05-25'),\n    updatedAt: new Date('2024-07-20'),\n  },\n  {\n    id: 'G005',\n    clientId: 'C005',\n    tenantId: 'zorgorganisatie',\n    title: 'Cognitieve training',\n    description: 'Geheugen- en concentratieoefeningen ter ondersteuning van cognitieve functies.',\n    status: 'active',\n    priority: 'high',\n    startDate: new Date('2024-07-20'),\n    endDate: new Date('2024-11-20'),\n    createdBy: '2',\n    createdAt: new Date('2024-07-15'),\n    updatedAt: new Date('2024-08-02'),\n  },\n];\n\n// Mock Reports Data\nexport const mockReports: Report[] = [\n  {\n    id: 'R001',\n    clientId: 'C001',\n    careGoalId: 'G001',\n    tenantId: 'zorgorganisatie',\n    title: 'Mobiliteit voortgang week 1',\n    content: 'Cliënt heeft deze week 5 van de 7 geplande oefeningen voltooid. Balans is merkbaar verbeterd.',\n    status: 'complete',\n    reportDate: new Date('2024-08-05'),\n    createdBy: '2',\n    createdAt: new Date('2024-08-05'),\n    updatedAt: new Date('2024-08-05'),\n  },\n  {\n    id: 'R002',\n    clientId: 'C001',\n    careGoalId: 'G002',\n    tenantId: 'zorgorganisatie',\n    title: 'Medicatie compliance evaluatie',\n    content: 'Medicatie wordt correct ingenomen volgens schema. Geen bijwerkingen gemeld.',\n    status: 'complete',\n    reportDate: new Date('2024-07-31'),\n    createdBy: '2',\n    createdAt: new Date('2024-07-31'),\n    updatedAt: new Date('2024-07-31'),\n  },\n  {\n    id: 'R003',\n    clientId: 'C002',\n    careGoalId: 'G003',\n    tenantId: 'zorgorganisatie',\n    title: 'Sociale activiteiten deelname',\n    content: 'Actieve deelname aan groepsactiviteiten. Positieve interactie met andere deelnemers.',\n    status: 'partial',\n    reportDate: new Date('2024-08-03'),\n    createdBy: '2',\n    createdAt: new Date('2024-08-04'),\n    updatedAt: new Date('2024-08-04'),\n  },\n  {\n    id: 'R004',\n    clientId: 'C003',\n    tenantId: 'zorgorganisatie',\n    title: 'Algemene observatie',\n    content: 'Algemene toestand stabiel. Geen bijzonderheden te melden.',\n    status: 'complete',\n    reportDate: new Date('2024-08-02'),\n    createdBy: '2',\n    createdAt: new Date('2024-08-02'),\n    updatedAt: new Date('2024-08-02'),\n  },\n  {\n    id: 'R005',\n    clientId: 'C005',\n    careGoalId: 'G005',\n    tenantId: 'zorgorganisatie',\n    title: 'Cognitieve training week 2',\n    content: '',\n    status: 'missing',\n    reportDate: new Date('2024-08-01'),\n    createdBy: '2',\n    createdAt: new Date('2024-08-06'),\n    updatedAt: new Date('2024-08-06'),\n  },\n];\n\n// Mock Schedule Items\nexport const mockScheduleItems: ScheduleItem[] = [\n  {\n    id: 'S001',\n    tenantId: 'zorgorganisatie',\n    clientId: 'C001',\n    staffId: '2',\n    title: 'Mobiliteit training',\n    description: 'Begeleide oefeningen voor mobiliteit',\n    startTime: new Date('2024-08-08T09:00:00'),\n    endTime: new Date('2024-08-08T10:00:00'),\n    status: 'scheduled',\n    careGoalIds: ['G001'],\n    createdBy: '2',\n    createdAt: new Date('2024-08-05'),\n    updatedAt: new Date('2024-08-05'),\n  },\n  {\n    id: 'S002',\n    tenantId: 'zorgorganisatie',\n    clientId: 'C002',\n    staffId: '2',\n    title: 'Groepsactiviteit',\n    description: 'Sociale activiteit in groepsverband',\n    startTime: new Date('2024-08-08T14:00:00'),\n    endTime: new Date('2024-08-08T15:30:00'),\n    status: 'scheduled',\n    careGoalIds: ['G003'],\n    createdBy: '2',\n    createdAt: new Date('2024-08-06'),\n    updatedAt: new Date('2024-08-06'),\n  },\n  {\n    id: 'S003',\n    tenantId: 'zorgorganisatie',\n    clientId: 'C005',\n    staffId: '2',\n    title: 'Cognitieve training',\n    description: 'Individuele cognitieve oefeningen',\n    startTime: new Date('2024-08-07T10:30:00'),\n    endTime: new Date('2024-08-07T11:30:00'),\n    status: 'completed',\n    careGoalIds: ['G005'],\n    createdBy: '2',\n    createdAt: new Date('2024-08-05'),\n    updatedAt: new Date('2024-08-07'),\n  },\n];\n\n// Mock Activity Logs\nexport const mockActivityLogs: ActivityLog[] = [\n  {\n    id: 'A001',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'CREATE_REPORT',\n    resource: 'Report',\n    resourceId: 'R001',\n    timestamp: new Date('2024-08-05T14:30:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A002',\n    tenantId: 'zorgorganisatie',\n    userId: '1',\n    action: 'UPDATE_USER',\n    resource: 'User',\n    resourceId: '2',\n    timestamp: new Date('2024-08-04T09:15:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A003',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'CREATE_SCHEDULE',\n    resource: 'ScheduleItem',\n    resourceId: 'S002',\n    timestamp: new Date('2024-08-06T11:20:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n];\n\n// Helper functions\nexport function getClientById(id: string): Client | undefined {\n  return mockClients.find(client => client.id === id);\n}\n\nexport function getCareGoalsByClientId(clientId: string): CareGoal[] {\n  return mockCareGoals.filter(goal => goal.clientId === clientId);\n}\n\nexport function getReportsByClientId(clientId: string): Report[] {\n  return mockReports.filter(report => report.clientId === clientId);\n}\n\nexport function getScheduleItemsByClientId(clientId: string): ScheduleItem[] {\n  return mockScheduleItems.filter(item => item.clientId === clientId);\n}\n\nexport function getActiveClients(): Client[] {\n  return mockClients.filter(client => client.isActive);\n}\n\nexport function getActiveCareGoals(): CareGoal[] {\n  return mockCareGoals.filter(goal => goal.status === 'active');\n}\n\nexport function getOverdueReports(): Report[] {\n  const threeDaysAgo = new Date();\n  threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\n  \n  return mockReports.filter(report => \n    report.status === 'missing' && report.reportDate < threeDaysAgo\n  );\n}\n\nexport function getTodayScheduleItems(): ScheduleItem[] {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  const tomorrow = new Date(today);\n  tomorrow.setDate(tomorrow.getDate() + 1);\n  \n  return mockScheduleItems.filter(item => \n    item.startTime >= today && item.startTime < tomorrow\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGO,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,gBAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,oBAAoC;IAC/C;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;YAAC;SAAO;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;YAAC;SAAO;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;YAAC;SAAO;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,mBAAkC;IAC7C;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;CACD;AAGM,SAAS,cAAc,EAAU;IACtC,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAClD;AAEO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AACxD;AAEO,SAAS,qBAAqB,QAAgB;IACnD,OAAO,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;AAC1D;AAEO,SAAS,2BAA2B,QAAgB;IACzD,OAAO,kBAAkB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AAC5D;AAEO,SAAS;IACd,OAAO,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ;AACrD;AAEO,SAAS;IACd,OAAO,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;AACtD;AAEO,SAAS;IACd,MAAM,eAAe,IAAI;IACzB,aAAa,OAAO,CAAC,aAAa,OAAO,KAAK;IAE9C,OAAO,YAAY,MAAM,CAAC,CAAA,SACxB,OAAO,MAAM,KAAK,aAAa,OAAO,UAAU,GAAG;AAEvD;AAEO,SAAS;IACd,MAAM,QAAQ,IAAI;IAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;IACxB,MAAM,WAAW,IAAI,KAAK;IAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;IAEtC,OAAO,kBAAkB,MAAM,CAAC,CAAA,OAC9B,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,GAAG;AAEhD", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/app/api/billing/metrics/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { mockBillingMetrics, mockTenants, mockUsers } from '@/lib/mockAdminData';\nimport { mockClients, mockReports, mockScheduleItems } from '@/lib/mockData';\nimport { BillingMetrics } from '@/types';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const tenantId = searchParams.get('tenantId');\n    const startDate = searchParams.get('startDate');\n    const endDate = searchParams.get('endDate');\n\n    // If specific tenant requested\n    if (tenantId) {\n      const metrics = calculateMetricsForTenant(tenantId, startDate, endDate);\n      return NextResponse.json({\n        success: true,\n        data: metrics,\n      });\n    }\n\n    // Return all tenant metrics\n    const allMetrics = mockTenants\n      .filter(tenant => tenant.isActive)\n      .map(tenant => calculateMetricsForTenant(tenant.id, startDate, endDate));\n\n    return NextResponse.json({\n      success: true,\n      data: allMetrics,\n    });\n\n  } catch (error) {\n    console.error('Billing metrics API error:', error);\n    return NextResponse.json(\n      { success: false, message: 'Er is een serverfout opgetreden' },\n      { status: 500 }\n    );\n  }\n}\n\nfunction calculateMetricsForTenant(\n  tenantId: string, \n  startDateStr?: string | null, \n  endDateStr?: string | null\n): BillingMetrics {\n  const startDate = startDateStr ? new Date(startDateStr) : new Date(new Date().getFullYear(), new Date().getMonth(), 1);\n  const endDate = endDateStr ? new Date(endDateStr) : new Date();\n\n  // Count staff members for this tenant\n  const staffCount = mockUsers.filter(user => \n    user.tenantId === tenantId && user.isActive\n  ).length;\n\n  // Count active clients for this tenant\n  const clientCount = mockClients.filter(client => \n    client.tenantId === tenantId && client.isActive\n  ).length;\n\n  // Count reports in date range for this tenant\n  const reportCount = mockReports.filter(report => {\n    const reportDate = new Date(report.reportDate);\n    return report.tenantId === tenantId && \n           reportDate >= startDate && \n           reportDate <= endDate;\n  }).length;\n\n  // Count schedule items in date range for this tenant\n  const scheduleItemCount = mockScheduleItems.filter(item => {\n    const itemDate = new Date(item.startTime);\n    return item.tenantId === tenantId && \n           itemDate >= startDate && \n           itemDate <= endDate;\n  }).length;\n\n  // Estimate activity count (simplified calculation)\n  const activityCount = reportCount + scheduleItemCount + (staffCount * 10);\n\n  return {\n    tenantId,\n    period: {\n      start: startDate,\n      end: endDate,\n    },\n    staffCount,\n    clientCount,\n    reportCount,\n    scheduleItemCount,\n    activityCount,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,+BAA+B;QAC/B,IAAI,UAAU;YACZ,MAAM,UAAU,0BAA0B,UAAU,WAAW;YAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF;QAEA,4BAA4B;QAC5B,MAAM,aAAa,6HAAA,CAAA,cAAW,CAC3B,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,EAChC,GAAG,CAAC,CAAA,SAAU,0BAA0B,OAAO,EAAE,EAAE,WAAW;QAEjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAkC,GAC7D;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,0BACP,QAAgB,EAChB,YAA4B,EAC5B,UAA0B;IAE1B,MAAM,YAAY,eAAe,IAAI,KAAK,gBAAgB,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,IAAI;IACpH,MAAM,UAAU,aAAa,IAAI,KAAK,cAAc,IAAI;IAExD,sCAAsC;IACtC,MAAM,aAAa,6HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAA,OAClC,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,EAC3C,MAAM;IAER,uCAAuC;IACvC,MAAM,cAAc,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,SACrC,OAAO,QAAQ,KAAK,YAAY,OAAO,QAAQ,EAC/C,MAAM;IAER,8CAA8C;IAC9C,MAAM,cAAc,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA;QACrC,MAAM,aAAa,IAAI,KAAK,OAAO,UAAU;QAC7C,OAAO,OAAO,QAAQ,KAAK,YACpB,cAAc,aACd,cAAc;IACvB,GAAG,MAAM;IAET,qDAAqD;IACrD,MAAM,oBAAoB,wHAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,CAAA;QACjD,MAAM,WAAW,IAAI,KAAK,KAAK,SAAS;QACxC,OAAO,KAAK,QAAQ,KAAK,YAClB,YAAY,aACZ,YAAY;IACrB,GAAG,MAAM;IAET,mDAAmD;IACnD,MAAM,gBAAgB,cAAc,oBAAqB,aAAa;IAEtE,OAAO;QACL;QACA,QAAQ;YACN,OAAO;YACP,KAAK;QACP;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}]}