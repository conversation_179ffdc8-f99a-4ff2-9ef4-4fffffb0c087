{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/lib/mockData.ts"], "sourcesContent": ["import { Client, CareGoal, Report, ScheduleItem, User, ActivityLog } from '@/types';\n\n// Mock Clients Data\nexport const mockClients: Client[] = [\n  {\n    id: 'C001',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C001',\n    isActive: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-08-01'),\n  },\n  {\n    id: 'C002',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C002',\n    isActive: true,\n    createdAt: new Date('2024-02-20'),\n    updatedAt: new Date('2024-07-28'),\n  },\n  {\n    id: 'C003',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C003',\n    isActive: true,\n    createdAt: new Date('2024-03-10'),\n    updatedAt: new Date('2024-08-05'),\n  },\n  {\n    id: 'C004',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C004',\n    isActive: false,\n    createdAt: new Date('2024-01-05'),\n    updatedAt: new Date('2024-06-15'),\n  },\n  {\n    id: 'C005',\n    tenantId: 'zorgorganisatie',\n    clientCode: 'C005',\n    isActive: true,\n    createdAt: new Date('2024-04-12'),\n    updatedAt: new Date('2024-08-03'),\n  },\n];\n\n// Mock Care Goals Data\nexport const mockCareGoals: CareGoal[] = [\n  {\n    id: 'G001',\n    clientId: 'C001',\n    tenantId: 'zorgorganisatie',\n    title: 'Mobiliteit verbeteren',\n    description: 'Dagelijkse oefeningen voor het verbeteren van de mobiliteit en balans.',\n    status: 'active',\n    priority: 'high',\n    startDate: new Date('2024-07-01'),\n    endDate: new Date('2024-09-01'),\n    createdBy: '2',\n    createdAt: new Date('2024-06-28'),\n    updatedAt: new Date('2024-08-01'),\n  },\n  {\n    id: 'G002',\n    clientId: 'C001',\n    tenantId: 'zorgorganisatie',\n    title: 'Medicatie compliance',\n    description: 'Zorgen voor correcte inname van voorgeschreven medicatie.',\n    status: 'completed',\n    priority: 'medium',\n    startDate: new Date('2024-06-01'),\n    endDate: new Date('2024-07-31'),\n    createdBy: '2',\n    createdAt: new Date('2024-05-28'),\n    updatedAt: new Date('2024-07-31'),\n  },\n  {\n    id: 'G003',\n    clientId: 'C002',\n    tenantId: 'zorgorganisatie',\n    title: 'Sociale activiteiten',\n    description: 'Deelname aan groepsactiviteiten ter bevordering van sociale interactie.',\n    status: 'active',\n    priority: 'medium',\n    startDate: new Date('2024-07-15'),\n    endDate: new Date('2024-10-15'),\n    createdBy: '2',\n    createdAt: new Date('2024-07-10'),\n    updatedAt: new Date('2024-08-05'),\n  },\n  {\n    id: 'G004',\n    clientId: 'C003',\n    tenantId: 'zorgorganisatie',\n    title: 'Voeding optimaliseren',\n    description: 'Verbetering van voedingspatroon en gewichtsbeheersing.',\n    status: 'paused',\n    priority: 'low',\n    startDate: new Date('2024-06-01'),\n    createdBy: '2',\n    createdAt: new Date('2024-05-25'),\n    updatedAt: new Date('2024-07-20'),\n  },\n  {\n    id: 'G005',\n    clientId: 'C005',\n    tenantId: 'zorgorganisatie',\n    title: 'Cognitieve training',\n    description: 'Geheugen- en concentratieoefeningen ter ondersteuning van cognitieve functies.',\n    status: 'active',\n    priority: 'high',\n    startDate: new Date('2024-07-20'),\n    endDate: new Date('2024-11-20'),\n    createdBy: '2',\n    createdAt: new Date('2024-07-15'),\n    updatedAt: new Date('2024-08-02'),\n  },\n];\n\n// Mock Reports Data\nexport const mockReports: Report[] = [\n  {\n    id: 'R001',\n    clientId: 'C001',\n    careGoalId: 'G001',\n    tenantId: 'zorgorganisatie',\n    title: 'Mobiliteit voortgang week 1',\n    content: 'Cliënt heeft deze week 5 van de 7 geplande oefeningen voltooid. Balans is merkbaar verbeterd.',\n    status: 'complete',\n    reportDate: new Date('2024-08-05'),\n    createdBy: '2',\n    createdAt: new Date('2024-08-05'),\n    updatedAt: new Date('2024-08-05'),\n  },\n  {\n    id: 'R002',\n    clientId: 'C001',\n    careGoalId: 'G002',\n    tenantId: 'zorgorganisatie',\n    title: 'Medicatie compliance evaluatie',\n    content: 'Medicatie wordt correct ingenomen volgens schema. Geen bijwerkingen gemeld.',\n    status: 'complete',\n    reportDate: new Date('2024-07-31'),\n    createdBy: '2',\n    createdAt: new Date('2024-07-31'),\n    updatedAt: new Date('2024-07-31'),\n  },\n  {\n    id: 'R003',\n    clientId: 'C002',\n    careGoalId: 'G003',\n    tenantId: 'zorgorganisatie',\n    title: 'Sociale activiteiten deelname',\n    content: 'Actieve deelname aan groepsactiviteiten. Positieve interactie met andere deelnemers.',\n    status: 'partial',\n    reportDate: new Date('2024-08-03'),\n    createdBy: '2',\n    createdAt: new Date('2024-08-04'),\n    updatedAt: new Date('2024-08-04'),\n  },\n  {\n    id: 'R004',\n    clientId: 'C003',\n    tenantId: 'zorgorganisatie',\n    title: 'Algemene observatie',\n    content: 'Algemene toestand stabiel. Geen bijzonderheden te melden.',\n    status: 'complete',\n    reportDate: new Date('2024-08-02'),\n    createdBy: '2',\n    createdAt: new Date('2024-08-02'),\n    updatedAt: new Date('2024-08-02'),\n  },\n  {\n    id: 'R005',\n    clientId: 'C005',\n    careGoalId: 'G005',\n    tenantId: 'zorgorganisatie',\n    title: 'Cognitieve training week 2',\n    content: '',\n    status: 'missing',\n    reportDate: new Date('2024-08-01'),\n    createdBy: '2',\n    createdAt: new Date('2024-08-06'),\n    updatedAt: new Date('2024-08-06'),\n  },\n];\n\n// Mock Schedule Items\nexport const mockScheduleItems: ScheduleItem[] = [\n  {\n    id: 'S001',\n    tenantId: 'zorgorganisatie',\n    clientId: 'C001',\n    staffId: '2',\n    title: 'Mobiliteit training',\n    description: 'Begeleide oefeningen voor mobiliteit',\n    startTime: new Date('2024-08-08T09:00:00'),\n    endTime: new Date('2024-08-08T10:00:00'),\n    status: 'scheduled',\n    careGoalIds: ['G001'],\n    createdBy: '2',\n    createdAt: new Date('2024-08-05'),\n    updatedAt: new Date('2024-08-05'),\n  },\n  {\n    id: 'S002',\n    tenantId: 'zorgorganisatie',\n    clientId: 'C002',\n    staffId: '2',\n    title: 'Groepsactiviteit',\n    description: 'Sociale activiteit in groepsverband',\n    startTime: new Date('2024-08-08T14:00:00'),\n    endTime: new Date('2024-08-08T15:30:00'),\n    status: 'scheduled',\n    careGoalIds: ['G003'],\n    createdBy: '2',\n    createdAt: new Date('2024-08-06'),\n    updatedAt: new Date('2024-08-06'),\n  },\n  {\n    id: 'S003',\n    tenantId: 'zorgorganisatie',\n    clientId: 'C005',\n    staffId: '2',\n    title: 'Cognitieve training',\n    description: 'Individuele cognitieve oefeningen',\n    startTime: new Date('2024-08-07T10:30:00'),\n    endTime: new Date('2024-08-07T11:30:00'),\n    status: 'completed',\n    careGoalIds: ['G005'],\n    createdBy: '2',\n    createdAt: new Date('2024-08-05'),\n    updatedAt: new Date('2024-08-07'),\n  },\n];\n\n// Mock Activity Logs\nexport const mockActivityLogs: ActivityLog[] = [\n  {\n    id: 'A001',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'CREATE_REPORT',\n    resource: 'Report',\n    resourceId: 'R001',\n    timestamp: new Date('2024-08-05T14:30:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A002',\n    tenantId: 'zorgorganisatie',\n    userId: '1',\n    action: 'UPDATE_USER',\n    resource: 'User',\n    resourceId: '2',\n    timestamp: new Date('2024-08-04T09:15:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n  {\n    id: 'A003',\n    tenantId: 'zorgorganisatie',\n    userId: '2',\n    action: 'CREATE_SCHEDULE',\n    resource: 'ScheduleItem',\n    resourceId: 'S002',\n    timestamp: new Date('2024-08-06T11:20:00'),\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n  },\n];\n\n// Helper functions\nexport function getClientById(id: string): Client | undefined {\n  return mockClients.find(client => client.id === id);\n}\n\nexport function getCareGoalsByClientId(clientId: string): CareGoal[] {\n  return mockCareGoals.filter(goal => goal.clientId === clientId);\n}\n\nexport function getReportsByClientId(clientId: string): Report[] {\n  return mockReports.filter(report => report.clientId === clientId);\n}\n\nexport function getScheduleItemsByClientId(clientId: string): ScheduleItem[] {\n  return mockScheduleItems.filter(item => item.clientId === clientId);\n}\n\nexport function getActiveClients(): Client[] {\n  return mockClients.filter(client => client.isActive);\n}\n\nexport function getActiveCareGoals(): CareGoal[] {\n  return mockCareGoals.filter(goal => goal.status === 'active');\n}\n\nexport function getOverdueReports(): Report[] {\n  const threeDaysAgo = new Date();\n  threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\n  \n  return mockReports.filter(report => \n    report.status === 'missing' && report.reportDate < threeDaysAgo\n  );\n}\n\nexport function getTodayScheduleItems(): ScheduleItem[] {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  const tomorrow = new Date(today);\n  tomorrow.setDate(tomorrow.getDate() + 1);\n  \n  return mockScheduleItems.filter(item => \n    item.startTime >= today && item.startTime < tomorrow\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGO,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,gBAA4B;IACvC;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,oBAAoC;IAC/C;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;YAAC;SAAO;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;YAAC;SAAO;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;YAAC;SAAO;QACrB,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,mBAAkC;IAC7C;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;CACD;AAGM,SAAS,cAAc,EAAU;IACtC,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAClD;AAEO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AACxD;AAEO,SAAS,qBAAqB,QAAgB;IACnD,OAAO,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;AAC1D;AAEO,SAAS,2BAA2B,QAAgB;IACzD,OAAO,kBAAkB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AAC5D;AAEO,SAAS;IACd,OAAO,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ;AACrD;AAEO,SAAS;IACd,OAAO,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;AACtD;AAEO,SAAS;IACd,MAAM,eAAe,IAAI;IACzB,aAAa,OAAO,CAAC,aAAa,OAAO,KAAK;IAE9C,OAAO,YAAY,MAAM,CAAC,CAAA,SACxB,OAAO,MAAM,KAAK,aAAa,OAAO,UAAU,GAAG;AAEvD;AAEO,SAAS;IACd,MAAM,QAAQ,IAAI;IAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;IACxB,MAAM,WAAW,IAAI,KAAK;IAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;IAEtC,OAAO,kBAAkB,MAAM,CAAC,CAAA,OAC9B,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,GAAG;AAEhD", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/app/api/dashboard/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { mockClients } from '@/lib/mockData';\nimport { mockScheduleItems } from '@/lib/mockData';\nimport { mockReports } from '@/lib/mockData';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Calculate dashboard metrics\n    const totalClients = mockClients.length;\n    const activeClients = mockClients.filter(client => client.isActive).length;\n    \n    // Today's schedule\n    const today = new Date().toISOString().split('T')[0];\n    const todaySchedule = mockScheduleItems.filter(item => \n      item.date === today\n    );\n    \n    // Pending goals (simplified calculation)\n    const pendingGoals = mockClients.reduce((total, client) => {\n      return total + (client.careGoals?.filter(goal => goal.status === 'in_progress').length || 0);\n    }, 0);\n    \n    // Overdue reports\n    const overdueReports = mockReports.filter(report => {\n      const reportDate = new Date(report.date);\n      const today = new Date();\n      return report.status !== 'complete' && reportDate < today;\n    }).length;\n    \n    // Recent activities (mock data)\n    const recentActivities = [\n      {\n        id: '1',\n        type: 'client_added',\n        description: 'Nieuwe cliënt toegevoegd',\n        timestamp: new Date().toISOString(),\n        user: 'Zorgverlener'\n      },\n      {\n        id: '2',\n        type: 'report_completed',\n        description: 'Rapportage voltooid voor C001',\n        timestamp: new Date(Date.now() - 3600000).toISOString(),\n        user: 'Zorgverlener'\n      },\n      {\n        id: '3',\n        type: 'appointment_scheduled',\n        description: 'Afspraak ingepland voor C003',\n        timestamp: new Date(Date.now() - 7200000).toISOString(),\n        user: 'Zorgverlener'\n      }\n    ];\n    \n    const dashboardData = {\n      metrics: {\n        totalClients,\n        activeClients,\n        pendingGoals,\n        overdueReports,\n        todayAppointments: todaySchedule.length\n      },\n      todaySchedule: todaySchedule.slice(0, 5), // Limit to 5 items\n      recentActivities: recentActivities.slice(0, 10), // Limit to 10 items\n      quickStats: {\n        completedReports: mockReports.filter(r => r.status === 'complete').length,\n        partialReports: mockReports.filter(r => r.status === 'partial').length,\n        missingReports: mockReports.filter(r => r.status === 'missing').length\n      }\n    };\n    \n    return NextResponse.json({\n      success: true,\n      data: dashboardData\n    });\n    \n  } catch (error) {\n    console.error('Dashboard API error:', error);\n    return NextResponse.json(\n      { success: false, message: 'Fout bij ophalen dashboard gegevens' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;AAIO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,8BAA8B;QAC9B,MAAM,eAAe,wHAAA,CAAA,cAAW,CAAC,MAAM;QACvC,MAAM,gBAAgB,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,EAAE,MAAM;QAE1E,mBAAmB;QACnB,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD,MAAM,gBAAgB,wHAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,CAAA,OAC7C,KAAK,IAAI,KAAK;QAGhB,yCAAyC;QACzC,MAAM,eAAe,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAC,OAAO;YAC9C,OAAO,QAAQ,CAAC,OAAO,SAAS,EAAE,OAAO,CAAA,OAAQ,KAAK,MAAM,KAAK,eAAe,UAAU,CAAC;QAC7F,GAAG;QAEH,kBAAkB;QAClB,MAAM,iBAAiB,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA;YACxC,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;YACvC,MAAM,QAAQ,IAAI;YAClB,OAAO,OAAO,MAAM,KAAK,cAAc,aAAa;QACtD,GAAG,MAAM;QAET,gCAAgC;QAChC,MAAM,mBAAmB;YACvB;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW,IAAI,OAAO,WAAW;gBACjC,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;gBACrD,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;gBACrD,MAAM;YACR;SACD;QAED,MAAM,gBAAgB;YACpB,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA,mBAAmB,cAAc,MAAM;YACzC;YACA,eAAe,cAAc,KAAK,CAAC,GAAG;YACtC,kBAAkB,iBAAiB,KAAK,CAAC,GAAG;YAC5C,YAAY;gBACV,kBAAkB,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;gBACzE,gBAAgB,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;gBACtE,gBAAgB,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;YACxE;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAsC,GACjE;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}