{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/app/api/auth/verify/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport jwt from 'jsonwebtoken';\nimport { User } from '@/types';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';\n\n// Mock gebruikers data - vervang dit met echte database calls\nconst mockUsers = [\n  {\n    id: '1',\n    email: '<EMAIL>',\n    name: '<PERSON><PERSON> Gebruiker',\n    role: 'admin' as const,\n    tenantId: 'zorgorganisatie',\n    isActive: true,\n    mfaEnabled: true,\n  },\n  {\n    id: '2',\n    email: '<EMAIL>',\n    name: 'Zorgverlener Test',\n    role: 'user' as const,\n    tenantId: 'zorgorganisatie',\n    isActive: true,\n    mfaEnabled: false,\n  },\n];\n\nexport async function GET(request: NextRequest) {\n  try {\n    const authHeader = request.headers.get('authorization');\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return NextResponse.json(\n        { success: false, message: 'Geen geldig token gevonden' },\n        { status: 401 }\n      );\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n\n    // Verifieer JWT token\n    const payload = jwt.verify(token, JWT_SECRET) as any;\n\n    // Zoek gebruiker in database (mock)\n    const user = mockUsers.find(u => u.id === payload.userId);\n    \n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: 'Gebruiker niet gevonden' },\n        { status: 401 }\n      );\n    }\n\n    if (!user.isActive) {\n      return NextResponse.json(\n        { success: false, message: 'Account is gedeactiveerd' },\n        { status: 401 }\n      );\n    }\n\n    // Gebruiker data zonder gevoelige informatie\n    const userData: User = {\n      id: user.id,\n      email: user.email,\n      name: user.name,\n      role: user.role,\n      tenantId: user.tenantId,\n      isActive: user.isActive,\n      lastLogin: new Date(),\n      mfaEnabled: user.mfaEnabled,\n    };\n\n    return NextResponse.json({\n      success: true,\n      user: userData,\n      message: 'Token is geldig',\n    });\n\n  } catch (error) {\n    console.error('Token verification error:', error);\n    \n    if (error instanceof jwt.JsonWebTokenError) {\n      return NextResponse.json(\n        { success: false, message: 'Ongeldig token' },\n        { status: 401 }\n      );\n    }\n\n    if (error instanceof jwt.TokenExpiredError) {\n      return NextResponse.json(\n        { success: false, message: 'Token is verlopen' },\n        { status: 401 }\n      );\n    }\n\n    return NextResponse.json(\n      { success: false, message: 'Er is een serverfout opgetreden' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAE7C,8DAA8D;AAC9D,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,YAAY;IACd;CACD;AAEM,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA6B,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QAEjE,sBAAsB;QACtB,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAElC,oCAAoC;QACpC,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,MAAM;QAExD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA0B,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA2B,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,6CAA6C;QAC7C,MAAM,WAAiB;YACrB,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,QAAQ;YACvB,UAAU,KAAK,QAAQ;YACvB,WAAW,IAAI;YACf,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAE3C,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAiB,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAoB,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAkC,GAC7D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}