module.exports = {

"[project]/.next-internal/server/app/api/clients/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/mockData.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getActiveCareGoals": ()=>getActiveCareGoals,
    "getActiveClients": ()=>getActiveClients,
    "getCareGoalsByClientId": ()=>getCareGoalsByClientId,
    "getClientById": ()=>getClientById,
    "getOverdueReports": ()=>getOverdueReports,
    "getReportsByClientId": ()=>getReportsByClientId,
    "getScheduleItemsByClientId": ()=>getScheduleItemsByClientId,
    "getTodayScheduleItems": ()=>getTodayScheduleItems,
    "mockActivityLogs": ()=>mockActivityLogs,
    "mockCareGoals": ()=>mockCareGoals,
    "mockClients": ()=>mockClients,
    "mockReports": ()=>mockReports,
    "mockScheduleItems": ()=>mockScheduleItems
});
const mockClients = [
    {
        id: 'C001',
        tenantId: 'zorgorganisatie',
        clientCode: 'C001',
        isActive: true,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-08-01')
    },
    {
        id: 'C002',
        tenantId: 'zorgorganisatie',
        clientCode: 'C002',
        isActive: true,
        createdAt: new Date('2024-02-20'),
        updatedAt: new Date('2024-07-28')
    },
    {
        id: 'C003',
        tenantId: 'zorgorganisatie',
        clientCode: 'C003',
        isActive: true,
        createdAt: new Date('2024-03-10'),
        updatedAt: new Date('2024-08-05')
    },
    {
        id: 'C004',
        tenantId: 'zorgorganisatie',
        clientCode: 'C004',
        isActive: false,
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date('2024-06-15')
    },
    {
        id: 'C005',
        tenantId: 'zorgorganisatie',
        clientCode: 'C005',
        isActive: true,
        createdAt: new Date('2024-04-12'),
        updatedAt: new Date('2024-08-03')
    }
];
const mockCareGoals = [
    {
        id: 'G001',
        clientId: 'C001',
        tenantId: 'zorgorganisatie',
        title: 'Mobiliteit verbeteren',
        description: 'Dagelijkse oefeningen voor het verbeteren van de mobiliteit en balans.',
        status: 'active',
        priority: 'high',
        startDate: new Date('2024-07-01'),
        endDate: new Date('2024-09-01'),
        createdBy: '2',
        createdAt: new Date('2024-06-28'),
        updatedAt: new Date('2024-08-01')
    },
    {
        id: 'G002',
        clientId: 'C001',
        tenantId: 'zorgorganisatie',
        title: 'Medicatie compliance',
        description: 'Zorgen voor correcte inname van voorgeschreven medicatie.',
        status: 'completed',
        priority: 'medium',
        startDate: new Date('2024-06-01'),
        endDate: new Date('2024-07-31'),
        createdBy: '2',
        createdAt: new Date('2024-05-28'),
        updatedAt: new Date('2024-07-31')
    },
    {
        id: 'G003',
        clientId: 'C002',
        tenantId: 'zorgorganisatie',
        title: 'Sociale activiteiten',
        description: 'Deelname aan groepsactiviteiten ter bevordering van sociale interactie.',
        status: 'active',
        priority: 'medium',
        startDate: new Date('2024-07-15'),
        endDate: new Date('2024-10-15'),
        createdBy: '2',
        createdAt: new Date('2024-07-10'),
        updatedAt: new Date('2024-08-05')
    },
    {
        id: 'G004',
        clientId: 'C003',
        tenantId: 'zorgorganisatie',
        title: 'Voeding optimaliseren',
        description: 'Verbetering van voedingspatroon en gewichtsbeheersing.',
        status: 'paused',
        priority: 'low',
        startDate: new Date('2024-06-01'),
        createdBy: '2',
        createdAt: new Date('2024-05-25'),
        updatedAt: new Date('2024-07-20')
    },
    {
        id: 'G005',
        clientId: 'C005',
        tenantId: 'zorgorganisatie',
        title: 'Cognitieve training',
        description: 'Geheugen- en concentratieoefeningen ter ondersteuning van cognitieve functies.',
        status: 'active',
        priority: 'high',
        startDate: new Date('2024-07-20'),
        endDate: new Date('2024-11-20'),
        createdBy: '2',
        createdAt: new Date('2024-07-15'),
        updatedAt: new Date('2024-08-02')
    }
];
const mockReports = [
    {
        id: 'R001',
        clientId: 'C001',
        careGoalId: 'G001',
        tenantId: 'zorgorganisatie',
        title: 'Mobiliteit voortgang week 1',
        content: 'Cliënt heeft deze week 5 van de 7 geplande oefeningen voltooid. Balans is merkbaar verbeterd.',
        status: 'complete',
        reportDate: new Date('2024-08-05'),
        createdBy: '2',
        createdAt: new Date('2024-08-05'),
        updatedAt: new Date('2024-08-05')
    },
    {
        id: 'R002',
        clientId: 'C001',
        careGoalId: 'G002',
        tenantId: 'zorgorganisatie',
        title: 'Medicatie compliance evaluatie',
        content: 'Medicatie wordt correct ingenomen volgens schema. Geen bijwerkingen gemeld.',
        status: 'complete',
        reportDate: new Date('2024-07-31'),
        createdBy: '2',
        createdAt: new Date('2024-07-31'),
        updatedAt: new Date('2024-07-31')
    },
    {
        id: 'R003',
        clientId: 'C002',
        careGoalId: 'G003',
        tenantId: 'zorgorganisatie',
        title: 'Sociale activiteiten deelname',
        content: 'Actieve deelname aan groepsactiviteiten. Positieve interactie met andere deelnemers.',
        status: 'partial',
        reportDate: new Date('2024-08-03'),
        createdBy: '2',
        createdAt: new Date('2024-08-04'),
        updatedAt: new Date('2024-08-04')
    },
    {
        id: 'R004',
        clientId: 'C003',
        tenantId: 'zorgorganisatie',
        title: 'Algemene observatie',
        content: 'Algemene toestand stabiel. Geen bijzonderheden te melden.',
        status: 'complete',
        reportDate: new Date('2024-08-02'),
        createdBy: '2',
        createdAt: new Date('2024-08-02'),
        updatedAt: new Date('2024-08-02')
    },
    {
        id: 'R005',
        clientId: 'C005',
        careGoalId: 'G005',
        tenantId: 'zorgorganisatie',
        title: 'Cognitieve training week 2',
        content: '',
        status: 'missing',
        reportDate: new Date('2024-08-01'),
        createdBy: '2',
        createdAt: new Date('2024-08-06'),
        updatedAt: new Date('2024-08-06')
    }
];
const mockScheduleItems = [
    {
        id: 'S001',
        tenantId: 'zorgorganisatie',
        clientId: 'C001',
        staffId: '2',
        title: 'Mobiliteit training',
        description: 'Begeleide oefeningen voor mobiliteit',
        startTime: new Date('2024-08-08T09:00:00'),
        endTime: new Date('2024-08-08T10:00:00'),
        status: 'scheduled',
        careGoalIds: [
            'G001'
        ],
        createdBy: '2',
        createdAt: new Date('2024-08-05'),
        updatedAt: new Date('2024-08-05')
    },
    {
        id: 'S002',
        tenantId: 'zorgorganisatie',
        clientId: 'C002',
        staffId: '2',
        title: 'Groepsactiviteit',
        description: 'Sociale activiteit in groepsverband',
        startTime: new Date('2024-08-08T14:00:00'),
        endTime: new Date('2024-08-08T15:30:00'),
        status: 'scheduled',
        careGoalIds: [
            'G003'
        ],
        createdBy: '2',
        createdAt: new Date('2024-08-06'),
        updatedAt: new Date('2024-08-06')
    },
    {
        id: 'S003',
        tenantId: 'zorgorganisatie',
        clientId: 'C005',
        staffId: '2',
        title: 'Cognitieve training',
        description: 'Individuele cognitieve oefeningen',
        startTime: new Date('2024-08-07T10:30:00'),
        endTime: new Date('2024-08-07T11:30:00'),
        status: 'completed',
        careGoalIds: [
            'G005'
        ],
        createdBy: '2',
        createdAt: new Date('2024-08-05'),
        updatedAt: new Date('2024-08-07')
    }
];
const mockActivityLogs = [
    {
        id: 'A001',
        tenantId: 'zorgorganisatie',
        userId: '2',
        action: 'CREATE_REPORT',
        resource: 'Report',
        resourceId: 'R001',
        timestamp: new Date('2024-08-05T14:30:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
        id: 'A002',
        tenantId: 'zorgorganisatie',
        userId: '1',
        action: 'UPDATE_USER',
        resource: 'User',
        resourceId: '2',
        timestamp: new Date('2024-08-04T09:15:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
        id: 'A003',
        tenantId: 'zorgorganisatie',
        userId: '2',
        action: 'CREATE_SCHEDULE',
        resource: 'ScheduleItem',
        resourceId: 'S002',
        timestamp: new Date('2024-08-06T11:20:00'),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
];
function getClientById(id) {
    return mockClients.find((client)=>client.id === id);
}
function getCareGoalsByClientId(clientId) {
    return mockCareGoals.filter((goal)=>goal.clientId === clientId);
}
function getReportsByClientId(clientId) {
    return mockReports.filter((report)=>report.clientId === clientId);
}
function getScheduleItemsByClientId(clientId) {
    return mockScheduleItems.filter((item)=>item.clientId === clientId);
}
function getActiveClients() {
    return mockClients.filter((client)=>client.isActive);
}
function getActiveCareGoals() {
    return mockCareGoals.filter((goal)=>goal.status === 'active');
}
function getOverdueReports() {
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    return mockReports.filter((report)=>report.status === 'missing' && report.reportDate < threeDaysAgo);
}
function getTodayScheduleItems() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return mockScheduleItems.filter((item)=>item.startTime >= today && item.startTime < tomorrow);
}
}),
"[project]/src/app/api/clients/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mockData.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const activeOnly = searchParams.get('active') === 'true';
        const search = searchParams.get('search');
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        let clients = activeOnly ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getActiveClients"])() : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockClients"];
        // Filter by search term
        if (search) {
            clients = clients.filter((client)=>client.clientCode.toLowerCase().includes(search.toLowerCase()));
        }
        // Pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedClients = clients.slice(startIndex, endIndex);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: paginatedClients,
            pagination: {
                page,
                limit,
                total: clients.length,
                totalPages: Math.ceil(clients.length / limit)
            }
        });
    } catch (error) {
        console.error('Clients API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: 'Er is een serverfout opgetreden'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const { clientCode } = await request.json();
        if (!clientCode) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Cliëntcode is verplicht'
            }, {
                status: 400
            });
        }
        // Check if client code already exists
        const existingClient = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockClients"].find((c)=>c.clientCode === clientCode);
        if (existingClient) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Cliëntcode bestaat al'
            }, {
                status: 409
            });
        }
        const newClient = {
            id: `C${String(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockClients"].length + 1).padStart(3, '0')}`,
            tenantId: 'zorgorganisatie',
            clientCode,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        // In real app, save to database
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockClients"].push(newClient);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: newClient,
            message: 'Cliënt succesvol toegevoegd'
        });
    } catch (error) {
        console.error('Create client error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: 'Er is een serverfout opgetreden'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1c5cf83f._.js.map