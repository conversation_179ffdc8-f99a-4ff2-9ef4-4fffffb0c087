{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/ZorgPortaalPlus_v2/frontendv2/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport jwt from \"jsonwebtoken\";\nimport { User } from \"@/types\";\n\n// Mock gebruikers data - vervang dit met echte database calls\nconst mockUsers = [\n  {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    password: \"admin123!\", // In productie: gehashed wachtwoord\n    name: \"Ad<PERSON> Gebruiker\",\n    role: \"admin\" as const,\n    tenantId: \"zorgorganisatie\",\n    isActive: true,\n    mfaEnabled: false,\n  },\n  {\n    id: \"2\",\n    email: \"<EMAIL>\",\n    password: \"zorg123!\", // In productie: gehashed wachtwoord\n    name: \"Zorgverlener Test\",\n    role: \"user\" as const,\n    tenantId: \"zorgorganisatie\",\n    isActive: true,\n    mfaEnabled: false,\n  },\n];\n\nconst JWT_SECRET =\n  process.env.JWT_SECRET || \"your-secret-key-change-in-production\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, password, mfaCode } = await request.json();\n\n    // Validatie\n    if (!email || !password) {\n      return NextResponse.json(\n        { success: false, message: \"E-mailadres en wachtwoord zijn verplicht\" },\n        { status: 400 }\n      );\n    }\n\n    // Zoek gebruiker\n    const user = mockUsers.find((u) => u.email === email);\n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: \"Ongeldige inloggegevens\" },\n        { status: 401 }\n      );\n    }\n\n    // Controleer wachtwoord (in productie: gebruik bcrypt)\n    if (user.password !== password) {\n      return NextResponse.json(\n        { success: false, message: \"Ongeldige inloggegevens\" },\n        { status: 401 }\n      );\n    }\n\n    // Controleer of gebruiker actief is\n    if (!user.isActive) {\n      return NextResponse.json(\n        { success: false, message: \"Account is gedeactiveerd\" },\n        { status: 401 }\n      );\n    }\n\n    // MFA controle\n    if (user.mfaEnabled && !mfaCode) {\n      return NextResponse.json(\n        {\n          success: false,\n          message: \"MFA verificatie vereist\",\n          requiresMFA: true,\n        },\n        { status: 401 }\n      );\n    }\n\n    if (user.mfaEnabled && mfaCode) {\n      // Mock MFA verificatie - in productie: gebruik echte MFA service\n      if (mfaCode !== \"123456\") {\n        return NextResponse.json(\n          { success: false, message: \"Ongeldige MFA code\" },\n          { status: 401 }\n        );\n      }\n    }\n\n    // Maak JWT token\n    const tokenPayload = {\n      userId: user.id,\n      email: user.email,\n      role: user.role,\n      tenantId: user.tenantId,\n    };\n\n    const token = jwt.sign(tokenPayload, JWT_SECRET, {\n      expiresIn: \"8h\", // 8 uur sessie\n    });\n\n    // Gebruiker data zonder wachtwoord\n    const userData: User = {\n      id: user.id,\n      email: user.email,\n      name: user.name,\n      role: user.role,\n      tenantId: user.tenantId,\n      isActive: user.isActive,\n      lastLogin: new Date(),\n      mfaEnabled: user.mfaEnabled,\n    };\n\n    return NextResponse.json({\n      success: true,\n      user: userData,\n      token,\n      message: \"Succesvol ingelogd\",\n    });\n  } catch (error) {\n    console.error(\"Login error:\", error);\n    return NextResponse.json(\n      { success: false, message: \"Er is een serverfout opgetreden\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGA,8DAA8D;AAC9D,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,YAAY;IACd;CACD;AAED,MAAM,aACJ,QAAQ,GAAG,CAAC,UAAU,IAAI;AAErB,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEvD,YAAY;QACZ,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA2C,GACtE;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,OAAO,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK;QAC/C,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA0B,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,uDAAuD;QACvD,IAAI,KAAK,QAAQ,KAAK,UAAU;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA0B,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,oCAAoC;QACpC,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA2B,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,IAAI,KAAK,UAAU,IAAI,CAAC,SAAS;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS;gBACT,aAAa;YACf,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,KAAK,UAAU,IAAI,SAAS;YAC9B,iEAAiE;YACjE,IAAI,YAAY,UAAU;gBACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS;gBAAqB,GAChD;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,iBAAiB;QACjB,MAAM,eAAe;YACnB,QAAQ,KAAK,EAAE;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,QAAQ;QACzB;QAEA,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,cAAc,YAAY;YAC/C,WAAW;QACb;QAEA,mCAAmC;QACnC,MAAM,WAAiB;YACrB,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,QAAQ;YACvB,UAAU,KAAK,QAAQ;YACvB,WAAW,IAAI;YACf,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAkC,GAC7D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}