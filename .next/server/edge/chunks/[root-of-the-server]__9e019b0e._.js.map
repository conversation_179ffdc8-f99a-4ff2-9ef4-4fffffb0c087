{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/ (unsupported edge import stream)"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`stream`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,MAAM,CAAC"}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/ (unsupported edge import crypto)"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`crypto`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,MAAM,CAAC"}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport jwt from 'jsonwebtoken';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';\n\n// Routes die authenticatie vereisen\nconst protectedRoutes = [\n  '/dashboard',\n  '/agenda',\n  '/clienten',\n  '/rapportages',\n  '/facturatie',\n  '/beheer',\n];\n\n// Routes die alleen voor admins zijn\nconst adminOnlyRoutes = [\n  '/facturatie',\n  '/beheer',\n];\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Check of de route beschermd is\n  const isProtectedRoute = protectedRoutes.some(route => \n    pathname.startsWith(route)\n  );\n\n  if (!isProtectedRoute) {\n    return NextResponse.next();\n  }\n\n  // Haal token uit cookies of Authorization header\n  const token = request.cookies.get('auth_token')?.value || \n                request.headers.get('authorization')?.replace('Bearer ', '');\n\n  if (!token) {\n    // Redirect naar login pagina\n    const loginUrl = new URL('/', request.url);\n    return NextResponse.redirect(loginUrl);\n  }\n\n  try {\n    // Verifieer JWT token\n    const payload = jwt.verify(token, JWT_SECRET) as any;\n\n    // Check of de route admin rechten vereist\n    const isAdminOnlyRoute = adminOnlyRoutes.some(route => \n      pathname.startsWith(route)\n    );\n\n    if (isAdminOnlyRoute && payload.role !== 'admin') {\n      // Redirect naar dashboard als gebruiker geen admin is\n      const dashboardUrl = new URL('/dashboard', request.url);\n      return NextResponse.redirect(dashboardUrl);\n    }\n\n    // Voeg user info toe aan headers voor gebruik in API routes\n    const response = NextResponse.next();\n    response.headers.set('x-user-id', payload.userId);\n    response.headers.set('x-user-email', payload.email);\n    response.headers.set('x-user-role', payload.role);\n    response.headers.set('x-tenant-id', payload.tenantId);\n\n    return response;\n\n  } catch (error) {\n    // Token is ongeldig, redirect naar login\n    const loginUrl = new URL('/', request.url);\n    return NextResponse.redirect(loginUrl);\n  }\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAE7C,oCAAoC;AACpC,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,qCAAqC;AACrC,MAAM,kBAAkB;IACtB;IACA;CACD;AAEM,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,iCAAiC;IACjC,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,SAAS,UAAU,CAAC;IAGtB,IAAI,CAAC,kBAAkB;QACrB,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,iDAAiD;IACjD,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;IAEvE,IAAI,CAAC,OAAO;QACV,6BAA6B;QAC7B,MAAM,WAAW,IAAI,IAAI,KAAK,QAAQ,GAAG;QACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,IAAI;QACF,sBAAsB;QACtB,MAAM,UAAU,6IAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAElC,0CAA0C;QAC1C,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,SAAS,UAAU,CAAC;QAGtB,IAAI,oBAAoB,QAAQ,IAAI,KAAK,SAAS;YAChD,sDAAsD;YACtD,MAAM,eAAe,IAAI,IAAI,cAAc,QAAQ,GAAG;YACtD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,4DAA4D;QAC5D,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;QAClC,SAAS,OAAO,CAAC,GAAG,CAAC,aAAa,QAAQ,MAAM;QAChD,SAAS,OAAO,CAAC,GAAG,CAAC,gBAAgB,QAAQ,KAAK;QAClD,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,IAAI;QAChD,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,QAAQ;QAEpD,OAAO;IAET,EAAE,OAAO,OAAO;QACd,yCAAyC;QACzC,MAAM,WAAW,IAAI,IAAI,KAAK,QAAQ,GAAG;QACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;AACF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}