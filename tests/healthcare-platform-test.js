#!/usr/bin/env node

/**
 * Comprehensive Healthcare Platform Test Script
 * Tests all functionality systematically with detailed reporting
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Test configuration
const CONFIG = {
  baseUrl: 'http://localhost:3002',
  timeout: 30000,
  screenshotDir: './test-screenshots',
  reportFile: './test-report.html',
  accounts: {
    admin: {
      email: '<EMAIL>',
      password: 'admin123!',
      mfa: '123456'
    },
    user: {
      email: '<EMAIL>',
      password: 'zorg123!'
    }
  },
  viewports: {
    mobile: { width: 375, height: 667 },
    tablet: { width: 768, height: 1024 },
    desktop: { width: 1024, height: 768 }
  }
};

// Test results storage
let testResults = [];
let currentTest = null;

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type.toUpperCase().padEnd(5);
  console.log(`[${timestamp}] ${prefix}: ${message}`);
}

function startTest(name, description) {
  currentTest = {
    name,
    description,
    startTime: Date.now(),
    status: 'running',
    steps: [],
    screenshots: [],
    errors: []
  };
  log(`Starting test: ${name}`, 'test');
}

function addStep(description, status = 'pass', details = null) {
  if (currentTest) {
    currentTest.steps.push({
      description,
      status,
      details,
      timestamp: Date.now()
    });
    log(`  ${status.toUpperCase()}: ${description}`, status);
  }
}

function finishTest(status = 'pass') {
  if (currentTest) {
    currentTest.status = status;
    currentTest.endTime = Date.now();
    currentTest.duration = currentTest.endTime - currentTest.startTime;
    testResults.push(currentTest);
    log(`Finished test: ${currentTest.name} (${status})`, status);
    currentTest = null;
  }
}

async function takeScreenshot(page, name) {
  try {
    if (!fs.existsSync(CONFIG.screenshotDir)) {
      fs.mkdirSync(CONFIG.screenshotDir, { recursive: true });
    }
    
    const filename = `${Date.now()}-${name}.png`;
    const filepath = path.join(CONFIG.screenshotDir, filename);
    await page.screenshot({ path: filepath, fullPage: true });
    
    if (currentTest) {
      currentTest.screenshots.push({ name, filepath, filename });
    }
    
    return filepath;
  } catch (error) {
    log(`Screenshot failed: ${error.message}`, 'error');
  }
}

async function waitForSelector(page, selector, timeout = CONFIG.timeout) {
  try {
    await page.waitForSelector(selector, { timeout });
    return true;
  } catch (error) {
    addStep(`Failed to find selector: ${selector}`, 'fail', error.message);
    return false;
  }
}

async function login(page, account) {
  try {
    await page.goto(CONFIG.baseUrl);
    await takeScreenshot(page, 'login-page');
    
    // Fill login form
    await page.type('input[name="email"]', account.email);
    await page.type('input[name="password"]', account.password);
    await takeScreenshot(page, 'login-filled');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Handle MFA if admin account
    if (account.mfa) {
      await page.waitForSelector('input[name="mfaCode"]', { timeout: 5000 });
      await page.type('input[name="mfaCode"]', account.mfa);
      await page.click('button[type="submit"]');
    }
    
    // Wait for redirect to dashboard
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    await takeScreenshot(page, 'logged-in');
    
    return true;
  } catch (error) {
    addStep('Login failed', 'fail', error.message);
    return false;
  }
}

// Test functions
async function testAuthentication(browser) {
  startTest('Authentication', 'Test login/logout flow with both accounts');
  
  const page = await browser.newPage();
  
  try {
    // Test admin login
    addStep('Testing admin login');
    const adminLogin = await login(page, CONFIG.accounts.admin);
    if (!adminLogin) {
      finishTest('fail');
      return;
    }
    
    // Test logout
    addStep('Testing logout');
    await page.click('button:has-text("Uitloggen")');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    
    // Test regular user login
    addStep('Testing regular user login');
    const userLogin = await login(page, CONFIG.accounts.user);
    if (!userLogin) {
      finishTest('fail');
      return;
    }
    
    addStep('Authentication tests completed successfully');
    finishTest('pass');
    
  } catch (error) {
    addStep('Authentication test failed', 'fail', error.message);
    finishTest('fail');
  } finally {
    await page.close();
  }
}

async function testClientManagement(browser) {
  startTest('Client Management', 'Test CRUD operations for clients');
  
  const page = await browser.newPage();
  
  try {
    await login(page, CONFIG.accounts.user);
    
    // Navigate to clients page
    addStep('Navigating to clients page');
    await page.click('a[href="/clienten"]');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    await takeScreenshot(page, 'clients-page');
    
    // Test add client
    addStep('Testing add client functionality');
    await page.click('button:has-text("Nieuwe Cliënt")');
    await page.waitForSelector('.healthcare-modal', { timeout: 5000 });
    await takeScreenshot(page, 'add-client-modal');
    
    // Fill client form
    await page.type('input[name="name"]', 'Test Client');
    await page.type('input[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    // Wait for modal to close and list to update
    await page.waitForSelector('.healthcare-modal', { hidden: true, timeout: 5000 });
    await takeScreenshot(page, 'client-added');
    
    // Test client detail view
    addStep('Testing client detail view');
    await page.click('.client-item:first-child');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    await takeScreenshot(page, 'client-detail');
    
    addStep('Client management tests completed successfully');
    finishTest('pass');
    
  } catch (error) {
    addStep('Client management test failed', 'fail', error.message);
    finishTest('fail');
  } finally {
    await page.close();
  }
}

async function testScheduleModule(browser) {
  startTest('Schedule Module', 'Test calendar and appointment functionality');
  
  const page = await browser.newPage();
  
  try {
    await login(page, CONFIG.accounts.user);
    
    // Navigate to agenda page
    addStep('Navigating to agenda page');
    await page.click('a[href="/agenda"]');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    await takeScreenshot(page, 'agenda-page');
    
    // Test add appointment
    addStep('Testing add appointment functionality');
    await page.click('button:has-text("Nieuwe Afspraak")');
    await page.waitForSelector('.healthcare-modal', { timeout: 5000 });
    await takeScreenshot(page, 'add-appointment-modal');
    
    // Fill appointment form
    await page.type('input[name="title"]', 'Test Appointment');
    await page.type('input[name="date"]', '2025-08-08');
    await page.type('input[name="time"]', '10:00');
    await page.click('button[type="submit"]');
    
    await page.waitForSelector('.healthcare-modal', { hidden: true, timeout: 5000 });
    await takeScreenshot(page, 'appointment-added');
    
    addStep('Schedule module tests completed successfully');
    finishTest('pass');
    
  } catch (error) {
    addStep('Schedule module test failed', 'fail', error.message);
    finishTest('fail');
  } finally {
    await page.close();
  }
}

async function testReportsModule(browser) {
  startTest('Reports Module', 'Test reports functionality and filtering');
  
  const page = await browser.newPage();
  
  try {
    await login(page, CONFIG.accounts.user);
    
    // Navigate to reports page
    addStep('Navigating to reports page');
    await page.click('a[href="/rapportages"]');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    await takeScreenshot(page, 'reports-page');
    
    // Test filter functionality
    addStep('Testing filter functionality');
    await page.click('select[name="status"]');
    await page.select('select[name="status"]', 'complete');
    await takeScreenshot(page, 'reports-filtered');
    
    // Test add report
    addStep('Testing add report functionality');
    await page.click('button:has-text("Nieuwe Rapportage")');
    await page.waitForSelector('.healthcare-modal', { timeout: 5000 });
    await takeScreenshot(page, 'add-report-modal');
    
    addStep('Reports module tests completed successfully');
    finishTest('pass');
    
  } catch (error) {
    addStep('Reports module test failed', 'fail', error.message);
    finishTest('fail');
  } finally {
    await page.close();
  }
}

async function testAdminFunctions(browser) {
  startTest('Admin Functions', 'Test admin-only functionality');
  
  const page = await browser.newPage();
  
  try {
    await login(page, CONFIG.accounts.admin);
    
    // Test admin navigation access
    addStep('Testing admin navigation access');
    const beheerLink = await page.$('a[href="/beheer"]');
    const facturatieLink = await page.$('a[href="/facturatie"]');
    
    if (!beheerLink || !facturatieLink) {
      addStep('Admin navigation links not found', 'fail');
      finishTest('fail');
      return;
    }
    
    // Navigate to admin page
    addStep('Navigating to admin page');
    await page.click('a[href="/beheer"]');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    await takeScreenshot(page, 'admin-page');
    
    // Navigate to billing page
    addStep('Navigating to billing page');
    await page.click('a[href="/facturatie"]');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    await takeScreenshot(page, 'billing-page');
    
    // Test export functionality
    addStep('Testing export functionality');
    const exportButton = await page.$('button:has-text("Export")');
    if (exportButton) {
      await exportButton.click();
      addStep('Export button clicked successfully');
    }
    
    addStep('Admin functions tests completed successfully');
    finishTest('pass');
    
  } catch (error) {
    addStep('Admin functions test failed', 'fail', error.message);
    finishTest('fail');
  } finally {
    await page.close();
  }
}

async function testResponsiveDesign(browser) {
  startTest('Responsive Design', 'Test layout on different screen sizes');
  
  const page = await browser.newPage();
  
  try {
    await login(page, CONFIG.accounts.user);
    
    for (const [deviceName, viewport] of Object.entries(CONFIG.viewports)) {
      addStep(`Testing ${deviceName} viewport (${viewport.width}x${viewport.height})`);
      
      await page.setViewport(viewport);
      await page.goto(`${CONFIG.baseUrl}/dashboard`);
      await takeScreenshot(page, `responsive-${deviceName}-dashboard`);
      
      // Test navigation on mobile
      if (deviceName === 'mobile') {
        addStep('Testing mobile navigation');
        const menuButton = await page.$('button[aria-label="Menu"]');
        if (menuButton) {
          await menuButton.click();
          await takeScreenshot(page, 'mobile-menu-open');
        }
      }
    }
    
    addStep('Responsive design tests completed successfully');
    finishTest('pass');
    
  } catch (error) {
    addStep('Responsive design test failed', 'fail', error.message);
    finishTest('fail');
  } finally {
    await page.close();
  }
}

// Generate HTML report
function generateReport() {
  const totalTests = testResults.length;
  const passedTests = testResults.filter(t => t.status === 'pass').length;
  const failedTests = testResults.filter(t => t.status === 'fail').length;
  
  const html = `
<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Healthcare Platform Test Report</title>
    <style>
        body { font-family: 'Inter', sans-serif; margin: 0; padding: 20px; background: #f9fafb; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 30px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 12px; text-align: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2rem; font-weight: bold; margin-bottom: 5px; }
        .pass { color: #10b981; }
        .fail { color: #ef4444; }
        .test-result { background: white; margin-bottom: 20px; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .test-header { padding: 20px; border-bottom: 1px solid #e5e7eb; }
        .test-title { font-size: 1.25rem; font-weight: 600; margin-bottom: 5px; }
        .test-description { color: #6b7280; }
        .test-meta { display: flex; justify-content: space-between; align-items: center; margin-top: 10px; }
        .test-status { padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 500; }
        .status-pass { background: #d1fae5; color: #065f46; }
        .status-fail { background: #fee2e2; color: #991b1b; }
        .test-steps { padding: 20px; }
        .step { padding: 10px; margin-bottom: 8px; border-radius: 8px; border-left: 4px solid; }
        .step-pass { background: #f0fdf4; border-color: #10b981; }
        .step-fail { background: #fef2f2; border-color: #ef4444; }
        .screenshots { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; }
        .screenshot { text-align: center; }
        .screenshot img { max-width: 100%; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .screenshot-name { margin-top: 8px; font-size: 0.875rem; color: #6b7280; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Healthcare Platform Test Report</h1>
            <p>Comprehensive test results for all platform functionality</p>
            <p><strong>Generated:</strong> ${new Date().toLocaleString('nl-NL')}</p>
        </div>
        
        <div class="summary">
            <div class="stat-card">
                <div class="stat-number">${totalTests}</div>
                <div>Total Tests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number pass">${passedTests}</div>
                <div>Passed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number fail">${failedTests}</div>
                <div>Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${Math.round((passedTests / totalTests) * 100)}%</div>
                <div>Success Rate</div>
            </div>
        </div>
        
        ${testResults.map(test => `
            <div class="test-result">
                <div class="test-header">
                    <div class="test-title">${test.name}</div>
                    <div class="test-description">${test.description}</div>
                    <div class="test-meta">
                        <span>Duration: ${test.duration}ms</span>
                        <span class="test-status status-${test.status}">${test.status.toUpperCase()}</span>
                    </div>
                </div>
                <div class="test-steps">
                    <h4>Test Steps:</h4>
                    ${test.steps.map(step => `
                        <div class="step step-${step.status}">
                            <strong>${step.status.toUpperCase()}:</strong> ${step.description}
                            ${step.details ? `<br><small>${step.details}</small>` : ''}
                        </div>
                    `).join('')}
                    
                    ${test.screenshots.length > 0 ? `
                        <h4>Screenshots:</h4>
                        <div class="screenshots">
                            ${test.screenshots.map(screenshot => `
                                <div class="screenshot">
                                    <img src="${screenshot.filename}" alt="${screenshot.name}">
                                    <div class="screenshot-name">${screenshot.name}</div>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `).join('')}
    </div>
</body>
</html>`;
  
  fs.writeFileSync(CONFIG.reportFile, html);
  log(`Test report generated: ${CONFIG.reportFile}`, 'info');
}

// Main test runner
async function runTests() {
  log('Starting Healthcare Platform Test Suite', 'info');
  
  const browser = await puppeteer.launch({
    headless: false, // Set to true for CI/CD
    defaultViewport: CONFIG.viewports.desktop,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    // Run all tests
    await testAuthentication(browser);
    await testClientManagement(browser);
    await testScheduleModule(browser);
    await testReportsModule(browser);
    await testAdminFunctions(browser);
    await testResponsiveDesign(browser);
    
    // Generate report
    generateReport();
    
    log('All tests completed!', 'info');
    log(`Results: ${testResults.filter(t => t.status === 'pass').length}/${testResults.length} passed`, 'info');
    
  } catch (error) {
    log(`Test suite failed: ${error.message}`, 'error');
  } finally {
    await browser.close();
  }
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, CONFIG };
