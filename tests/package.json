{"name": "healthcare-platform-tests", "version": "1.0.0", "description": "Comprehensive test suite for Healthcare Platform", "main": "healthcare-platform-test.js", "scripts": {"test": "node healthcare-platform-test.js", "test:headless": "HEADLESS=true node healthcare-platform-test.js", "install-deps": "npm install puppeteer"}, "dependencies": {"puppeteer": "^21.0.0"}, "keywords": ["healthcare", "testing", "automation", "puppeteer"], "author": "Healthcare Platform Team", "license": "MIT"}