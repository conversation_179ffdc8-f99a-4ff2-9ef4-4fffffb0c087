#!/bin/bash

# Simple Healthcare Platform Test Script
# Tests basic functionality using curl and basic checks

set -e

BASE_URL="http://localhost:3002"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="test-report-${TIMESTAMP}.txt"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$REPORT_FILE"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1" | tee -a "$REPORT_FILE"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1" | tee -a "$REPORT_FILE"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$REPORT_FILE"
}

start_test() {
    ((TOTAL_TESTS++))
    log_info "Testing: $1"
}

# Test functions
test_server_running() {
    start_test "Server availability"
    
    if curl -s --connect-timeout 5 "$BASE_URL" > /dev/null; then
        log_success "Server is running and accessible"
        return 0
    else
        log_error "Server is not accessible at $BASE_URL"
        return 1
    fi
}

test_login_page() {
    start_test "Login page loads"
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL" -o /tmp/login_page.html)
    
    if [ "$response" = "200" ]; then
        if grep -q "ZorgPortaal Plus" /tmp/login_page.html && grep -q "healthcare-card" /tmp/login_page.html; then
            log_success "Login page loads correctly with healthcare styling"
        else
            log_error "Login page missing expected content or styling"
        fi
    else
        log_error "Login page returned HTTP $response"
    fi
}

test_style_preview() {
    start_test "Style preview page"
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/style-preview" -o /tmp/style_preview.html)
    
    if [ "$response" = "200" ]; then
        if grep -q "Healthcare Platform Styling" /tmp/style_preview.html && grep -q "healthcare-theme" /tmp/style_preview.html; then
            log_success "Style preview page loads with healthcare theme"
        else
            log_error "Style preview page missing expected styling elements"
        fi
    else
        log_error "Style preview page returned HTTP $response"
    fi
}

test_api_endpoints() {
    start_test "API endpoints accessibility"
    
    # Test clients API
    clients_response=$(curl -s -w "%{http_code}" "$BASE_URL/api/clients" -o /tmp/clients_api.json)
    if [ "$clients_response" = "200" ]; then
        log_success "Clients API endpoint accessible"
    else
        log_error "Clients API endpoint returned HTTP $clients_response"
    fi
    
    # Test dashboard API
    dashboard_response=$(curl -s -w "%{http_code}" "$BASE_URL/api/dashboard" -o /tmp/dashboard_api.json)
    if [ "$dashboard_response" = "200" ]; then
        log_success "Dashboard API endpoint accessible"
    else
        log_error "Dashboard API endpoint returned HTTP $dashboard_response"
    fi
    
    # Test schedule API
    schedule_response=$(curl -s -w "%{http_code}" "$BASE_URL/api/schedule" -o /tmp/schedule_api.json)
    if [ "$schedule_response" = "200" ]; then
        log_success "Schedule API endpoint accessible"
    else
        log_error "Schedule API endpoint returned HTTP $schedule_response"
    fi
}

test_static_assets() {
    start_test "Static assets loading"

    # Test if CSS is included in the main page
    if curl -s "$BASE_URL" | grep -q "stylesheet.*css"; then
        log_success "CSS assets are referenced correctly"
    else
        log_error "CSS assets not found in page"
    fi
}

test_healthcare_styling() {
    start_test "Healthcare styling implementation"
    
    # Check if healthcare CSS classes are present in the main page
    if curl -s "$BASE_URL" | grep -q "healthcare-"; then
        log_success "Healthcare CSS classes found in main page"
    else
        log_error "Healthcare CSS classes not found in main page"
    fi
    
    # Check style preview for comprehensive styling
    if curl -s "$BASE_URL/style-preview" | grep -q "healthcare-btn\|healthcare-card\|healthcare-theme"; then
        log_success "Comprehensive healthcare styling implemented"
    else
        log_error "Healthcare styling not fully implemented"
    fi
}

test_responsive_meta() {
    start_test "Responsive design meta tags"
    
    if curl -s "$BASE_URL" | grep -q 'viewport.*width=device-width'; then
        log_success "Responsive viewport meta tag present"
    else
        log_error "Responsive viewport meta tag missing"
    fi
}

test_accessibility_features() {
    start_test "Basic accessibility features"
    
    page_content=$(curl -s "$BASE_URL")
    
    # Check for proper HTML structure
    if echo "$page_content" | grep -q 'lang="nl"'; then
        log_success "HTML lang attribute set correctly"
    else
        log_error "HTML lang attribute missing or incorrect"
    fi
    
    # Check for semantic HTML
    if echo "$page_content" | grep -q '<main\|<nav\|<header'; then
        log_success "Semantic HTML elements found"
    else
        log_warning "Limited semantic HTML elements found"
    fi
}

test_dutch_language() {
    start_test "Dutch language implementation"
    
    page_content=$(curl -s "$BASE_URL")
    
    # Check for Dutch text
    if echo "$page_content" | grep -q "ZorgPortaal\|Inloggen\|Wachtwoord"; then
        log_success "Dutch language text found"
    else
        log_error "Dutch language text not found"
    fi
}

# Performance tests
test_page_load_performance() {
    start_test "Page load performance"
    
    start_time=$(date +%s%N)
    curl -s "$BASE_URL" > /dev/null
    end_time=$(date +%s%N)
    
    load_time=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
    
    if [ "$load_time" -lt 3000 ]; then
        log_success "Page loads in ${load_time}ms (good performance)"
    elif [ "$load_time" -lt 5000 ]; then
        log_warning "Page loads in ${load_time}ms (acceptable performance)"
    else
        log_error "Page loads in ${load_time}ms (poor performance)"
    fi
}

# Generate summary report
generate_summary() {
    echo "" | tee -a "$REPORT_FILE"
    echo "========================================" | tee -a "$REPORT_FILE"
    echo "HEALTHCARE PLATFORM TEST SUMMARY" | tee -a "$REPORT_FILE"
    echo "========================================" | tee -a "$REPORT_FILE"
    echo "Timestamp: $(date)" | tee -a "$REPORT_FILE"
    echo "Total Tests: $TOTAL_TESTS" | tee -a "$REPORT_FILE"
    echo "Passed: $PASSED_TESTS" | tee -a "$REPORT_FILE"
    echo "Failed: $FAILED_TESTS" | tee -a "$REPORT_FILE"
    
    if [ "$FAILED_TESTS" -eq 0 ]; then
        echo -e "${GREEN}SUCCESS RATE: 100%${NC}" | tee -a "$REPORT_FILE"
        echo -e "${GREEN}All tests passed! ✅${NC}" | tee -a "$REPORT_FILE"
    else
        success_rate=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
        echo -e "${YELLOW}SUCCESS RATE: ${success_rate}%${NC}" | tee -a "$REPORT_FILE"
        echo -e "${RED}Some tests failed ❌${NC}" | tee -a "$REPORT_FILE"
    fi
    
    echo "========================================" | tee -a "$REPORT_FILE"
    echo "Report saved to: $REPORT_FILE"
}

# Main test execution
main() {
    echo "🏥 Healthcare Platform Test Suite"
    echo "=================================="
    echo "Starting comprehensive testing..."
    echo ""
    
    # Initialize report
    echo "Healthcare Platform Test Report - $(date)" > "$REPORT_FILE"
    echo "==========================================" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # Run all tests
    test_server_running || exit 1
    test_login_page
    test_style_preview
    test_api_endpoints
    test_static_assets
    test_healthcare_styling
    test_responsive_meta
    test_accessibility_features
    test_dutch_language
    test_page_load_performance
    
    # Generate summary
    generate_summary
    
    # Exit with appropriate code
    if [ "$FAILED_TESTS" -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Run main function
main "$@"
